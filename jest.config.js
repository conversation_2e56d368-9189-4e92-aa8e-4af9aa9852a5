const nextJest = require('next/jest')

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: './'
})

// Add any custom config to be passed to Jest
const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.js'],
  testEnvironment: 'jest-environment-jsdom',
  moduleNameMapper: {
    // Handle module aliases
    '^src/(.*)$': '<rootDir>/src/$1',
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@core/(.*)$': '<rootDir>/src/@core/$1',
    '^@fake-db/(.*)$': '<rootDir>/src/@fake-db/$1',
    
    // Handle CSS imports (with CSS modules)
    '^.+\\.module\\.(css|sass|scss)$': 'identity-obj-proxy',
    
    // Handle CSS imports (without CSS modules)
    '^.+\\.(css|sass|scss)$': '<rootDir>/__mocks__/styleMock.js',
    
    // Handle image imports
    '^.+\\.(png|jpg|jpeg|gif|webp|avif|ico|bmp|svg)$/i': '<rootDir>/__mocks__/fileMock.js',
    
    // Handle font imports
    '^.+\\.(woff|woff2|eot|ttf|otf)$': '<rootDir>/__mocks__/fileMock.js',
  },
  transformIgnorePatterns: [
    'node_modules/(?!(.*\\.mjs$|@mui|@iconify))'
  ],
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/@core/**',
    '!src/@fake-db/**',
    '!src/views/**',
    '!src/layouts/**',
    '!src/iconify-bundle/**',
    '!src/setupTests.js',
    '!src/**/*.config.js',
    '!**/__tests__/**',
    '!**/tests/**',
    '!**/node_modules/**',
    '!**/coverage/**',
    // Include contact-groups specifically
    'src/pages/contact-groups/**/*.{js,jsx,ts,tsx}',
    // Include specific pages for coverage
    'src/pages/register/**/*.{js,jsx,ts,tsx}',
    'src/pages/donation-receipts/**/*.{js,jsx,ts,tsx}',
    'src/pages/profile/**/*.{js,jsx,ts,tsx}',
    // Include auth-related files for coverage
    'src/context/AuthContext.js',
    'src/pages/login/index.js',
    'src/@core/components/auth/AuthGuard.js',
    // Include helpers and utils
    'src/helpers/**/*.{js,jsx,ts,tsx}',
    'src/utils/**/*.{js,jsx,ts,tsx}',
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  coverageThreshold: {
    global: {
      branches: 40,
      functions: 40,
      lines: 40,
      statements: 40,
    },
    // Specific thresholds for target pages
    'src/pages/register/': {
      branches: 50,
      functions: 50,
      lines: 50,
      statements: 50,
    },
    'src/pages/donation-receipts/': {
      branches: 50,
      functions: 50,
      lines: 50,
      statements: 50,
    },
    'src/pages/profile/': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95,
    },
    'src/pages/profile/sections-longform/': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95,
    },
    'src/pages/donor/': {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85,
    },
    'src/pages/donors/': {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85,
    },
    'src/pages/donation-head/': {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85,
    },
    // Add coverage threshold for contact-groups
    'src/pages/contact-groups/': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95,
    },
  },
  testMatch: [
    '<rootDir>/src/tests/**/*.test.{js,jsx,ts,tsx}',
    '<rootDir>/src/tests/**/*.spec.{js,jsx,ts,tsx}',
  ],
  testTimeout: 30000,
  verbose: true,
  // Add globals for better testing experience
  globals: {
    'ts-jest': {
      tsconfig: 'tsconfig.jest.json'
    }
  },
  // Support for ES modules
  extensionsToTreatAsEsm: ['.ts', '.tsx'],
  // Ignore specific test patterns that might cause issues
  testPathIgnorePatterns: [
    '<rootDir>/.next/',
    '<rootDir>/node_modules/',
    '<rootDir>/coverage/',
  ],
  // Mock specific modules that cause issues in test environment
  modulePathIgnorePatterns: [
    '<rootDir>/.next/',
  ],
  // Exclude problematic files from coverage
  coveragePathIgnorePatterns: [
    '/node_modules/',
    '/coverage/',
    'src/pages/user-management/roles/DialogData.js', // Exclude file with syntax errors
  ],
}

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = async (...args) => {
  const fn = createJestConfig(customJestConfig)
  const res = await fn(...args)

  // Ensure our module name mapping is preserved and use correct property name
  res.moduleNameMapper = {
    ...res.moduleNameMapper,
    '^src/(.*)$': '<rootDir>/src/$1',
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@core/(.*)$': '<rootDir>/src/@core/$1',
    '^@fake-db/(.*)$': '<rootDir>/src/@fake-db/$1',
  }

  return res
}
