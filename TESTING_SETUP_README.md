# Jest Testing Setup for Next.js Donation Receipt Application

This document provides comprehensive instructions for running and managing tests in the donation receipt management application.

## 📋 Table of Contents

- [Installation](#installation)
- [Test Structure](#test-structure)
- [Running Tests](#running-tests)
- [Test Coverage](#test-coverage)
- [Test Configuration](#test-configuration)
- [Writing Tests](#writing-tests)
- [Troubleshooting](#troubleshooting)

## 🚀 Installation

### Prerequisites

Ensure you have Node.js (v16 or higher) and npm installed.

### Install Dependencies

```bash
npm install
```

The following testing dependencies are included:

- **Jest**: Test framework
- **@testing-library/react**: React component testing utilities
- **@testing-library/jest-dom**: Custom Jest matchers
- **@testing-library/user-event**: User interaction simulation
- **jest-environment-jsdom**: DOM environment for testing
- **TypeScript support**: For .ts/.tsx files
- **identity-obj-proxy**: CSS modules mocking

## 📁 Test Structure

```
src/tests/
├── unit/
│   ├── context/
│   │   └── AuthContext.test.js
│   └── pages/
│       ├── register/
│       │   └── index.test.js
│       ├── donation-receipts/
│       │   └── index.test.js
│       └── profile/
│           └── index.test.js
├── integration/
├── api/
└── utils/
```

### Test File Naming Convention

- Unit tests: `*.test.js` or `*.spec.js`
- Integration tests: `*.integration.test.js`
- API tests: `*.api.test.js`

## 🏃‍♂️ Running Tests

### Basic Commands

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test categories
npm run test:unit
npm run test:integration
npm run test:api

# Run specific page tests
npm run test:pages
npm run test:register
npm run test:donation-receipts
npm run test:profile

# Debug tests
npm run test:debug
```

### Running Specific Tests

```bash
# Run a specific test file
npm test src/tests/unit/pages/register/index.test.js

# Run tests matching a pattern
npm test -- --testNamePattern="should render"

# Run tests for a specific component
npm test -- --testPathPattern=register
```

## 📊 Test Coverage

### Coverage Reports

The project generates multiple coverage report formats:

- **Terminal**: Summary in console
- **HTML**: Interactive report in `coverage/lcov-report/index.html`
- **LCOV**: Machine-readable format in `coverage/lcov.info`
- **JSON**: Detailed data in `coverage/coverage-final.json`

### Coverage Thresholds

| Component | Branches | Functions | Lines | Statements |
|-----------|----------|-----------|-------|------------|
| Global | 40% | 40% | 40% | 40% |
| Register Page | 50% | 50% | 50% | 50% |
| Donation Receipts | 50% | 50% | 50% | 50% |
| Profile Page | 50% | 50% | 50% | 50% |

### Viewing Coverage

```bash
# Generate and open coverage report
npm run test:coverage:open

# View coverage in browser
open coverage/lcov-report/index.html
```

## ⚙️ Test Configuration

### Jest Configuration (`jest.config.js`)

Key features:

- **Next.js Integration**: Uses `next/jest` for seamless Next.js testing
- **TypeScript Support**: Handles .ts/.tsx files
- **Module Aliases**: Supports src/, @/, @core/ path mapping
- **CSS/Asset Mocking**: Handles CSS modules and static assets
- **Extended Timeout**: 30-second timeout for complex tests

### Setup Files

- **`src/setupTests.js`**: Global test setup and mocks
- **`tsconfig.jest.json`**: TypeScript configuration for tests
- **`__mocks__/`**: Mock files for styles and assets

## ✍️ Writing Tests

### Test Structure Template

```javascript
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ComponentName from 'src/path/to/component';

// Mock dependencies
jest.mock('dependency-name');

describe('ComponentName', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Feature Group', () => {
    test('should perform specific behavior', async () => {
      const user = userEvent.setup();
      render(<ComponentName />);
      
      // Test implementation
      expect(screen.getByText('Expected Text')).toBeInTheDocument();
    });
  });
});
```

### Best Practices

1. **Group Related Tests**: Use `describe` blocks for logical grouping
2. **Clear Test Names**: Use descriptive names starting with "should"
3. **Mock External Dependencies**: Mock APIs, routers, and external libraries
4. **Test User Interactions**: Use `@testing-library/user-event` for realistic interactions
5. **Wait for Async Operations**: Use `waitFor` for async state changes
6. **Clean Up**: Clear mocks between tests

### Common Testing Patterns

#### Testing Form Submissions

```javascript
test('should submit form with valid data', async () => {
  const user = userEvent.setup();
  render(<FormComponent />);
  
  await user.type(screen.getByLabelText(/name/i), 'John Doe');
  await user.click(screen.getByRole('button', { name: /submit/i }));
  
  await waitFor(() => {
    expect(mockApiCall).toHaveBeenCalledWith(expectedData);
  });
});
```

#### Testing API Calls

```javascript
test('should handle API errors', async () => {
  axios.mockRejectedValueOnce(new Error('API Error'));
  
  render(<Component />);
  
  await waitFor(() => {
    expect(screen.getByText(/error message/i)).toBeInTheDocument();
  });
});
```

#### Testing Conditional Rendering

```javascript
test('should show donor profile for donor users', () => {
  render(<ProfileComponent user={{ role: 'donor' }} />);
  
  expect(screen.getByTestId('donor-profile')).toBeInTheDocument();
  expect(screen.queryByTestId('ngo-profile')).not.toBeInTheDocument();
});
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Module Resolution Errors

**Problem**: Cannot resolve module paths
**Solution**: Check `moduleNameMapper` in `jest.config.js`

#### 2. CSS Import Errors

**Problem**: CSS imports causing test failures
**Solution**: Ensure `styleMock.js` and `identity-obj-proxy` are configured

#### 3. Async Test Timeouts

**Problem**: Tests timing out on async operations
**Solution**: Increase timeout or use proper `waitFor` patterns

```javascript
// Increase timeout for specific test
test('long running test', async () => {
  // test code
}, 60000); // 60 second timeout
```

#### 4. Next.js Specific Issues

**Problem**: Next.js features not working in tests
**Solution**: Ensure proper mocking of Next.js components

```javascript
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    pathname: '/test',
  }),
}));
```

### Debug Mode

```bash
# Run tests in debug mode
npm run test:debug

# Then attach debugger on port 9229
```

### Environment Variables

Tests use development environment by default. Override with:

```bash
NODE_ENV=test npm test
```

## 📋 Test Checklist

When writing tests for a new component:

- [ ] **Rendering**: Component renders without crashing
- [ ] **Props**: Component handles different prop combinations
- [ ] **User Interactions**: Click, type, form submissions work
- [ ] **API Calls**: Mock and test API integrations
- [ ] **Error States**: Handle and display errors appropriately
- [ ] **Loading States**: Show loading indicators
- [ ] **Permissions**: Respect RBAC and user roles
- [ ] **Responsive**: Works across different screen sizes
- [ ] **Accessibility**: Screen readers and keyboard navigation

## 📚 Additional Resources

- [React Testing Library Documentation](https://testing-library.com/docs/react-testing-library/intro/)
- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [Next.js Testing Guide](https://nextjs.org/docs/testing)
- [Testing Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)

## 🤝 Contributing

When adding new tests:

1. Follow the existing test structure and naming conventions
2. Ensure tests are deterministic and don't rely on external services
3. Add appropriate mocks for dependencies
4. Maintain coverage thresholds
5. Document any new testing utilities or patterns

---

**Happy Testing! 🧪** 