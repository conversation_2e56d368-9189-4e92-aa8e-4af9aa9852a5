import PropTypes from "prop-types";
import { useContext, useEffect, useRef, useState } from "react";

// ** MUI Imports
import CardContent from "@mui/material/CardContent";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";

// ** Third Party Imports
import { Controller, useForm } from "react-hook-form";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
const OrganisationDetails = ({ formData, onUpdate }) => {
  const {
    control,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: formData?.organisationDetails || {}, // Initialize form fields with existing data
    mode: "onChange"
  });

  const panRegex = /^[A-Z]{5}\d{4}[A-Z]$/;
  const validFourthChars = ["P", "C", "H", "F", "A", "T", "B", "L", "J", "G"];

  const {getAllListValuesByListNameId} = useContext(AuthContext)
  // Watch all fields for changes
  const watchedFields = watch();

  // Track previous watched fields using useRef
  const previousWatchedFields = useRef();
  useEffect(() => {
    // Compare previous watched fields with current watched fields
    if (
      JSON.stringify(previousWatchedFields.current) !==
      JSON.stringify(watchedFields)
    ) {
      onUpdate(watchedFields); // Send only the updated fields to the parent
      previousWatchedFields.current = watchedFields; // Update the ref with the latest value
    }
  }, [watchedFields, onUpdate]);
  const [listOfStates, setListOfStates] = useState([]);
  const [locate, setLocate] = useState(formData?.organisationDetails?.state);

    const handleError = (error) => {
      console.error("states error:", error);
    };
    useEffect(() => {
        getAllListValuesByListNameId(
          authConfig.stateListNameId,
          (data) => {
            setListOfStates(
              data?.listValues.map((item) => ({
                value: item.id,
                key: item.listValue,
              }))
            );
          },
          handleError
        );
    }, [authConfig]);
  
  const handleLocationChange = (newValue) => {
    setLocate(newValue);
  };

  return (
      <CardContent>
        <Grid
          container
          spacing={3}
          sx={{
            width: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
            <FormControl fullWidth>
              <Controller
                name="trustName"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="text"
                    label="Trust Name"
                    placeholder="Enter your trust name"
                    error={Boolean(errors.trustName)}
                    helperText={errors.trustName?.message}
                    size="small"
                    aria-describedby="validation-trust-name"
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
            <FormControl fullWidth>
              <Controller
                name="registrationNo"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="text"
                    label="Registration Number"
                    placeholder="Enter your Registration Number"
                    error={Boolean(errors.registrationNo)}
                    helperText={errors.registrationNo?.message}
                    size="small"
                    aria-describedby="validation-registrationNo"
                    inputProps={{ maxLength: 30 }}
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          <FormControl fullWidth>
                  <Controller
                    name="panNo"
                    control={control}
                    rules={{
                      required: false,
                      validate: (value) => {
                        if (!value) return "";
                        if (!panRegex.test(value)) {
                          return "Invalid PAN format. Must be 5 letters, 4 digits, 1 letter (e.g., **********)";
                        }
                        if (!validFourthChars.includes(value.charAt(3))) {
                          return "Invalid 4th character. Must be one of: P, C, H, F, A, T, B, L, J, G";
                        }
                        return "";
                      },
                    }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                        size="small"
                        label="PAN Number"
                        placeholder="Enter your PAN Number"
                        error={Boolean(errors.panNo)}
                        helperText={errors.panNo?.message}
                        inputProps={{ maxLength: 10 }}
                      />
                    )}
                  />
                </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
            <FormControl fullWidth>
              <Controller
                name="g80RegistrationNo"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="text"
                    label="80G Registration Number"
                    placeholder="Enter your Number"
                    error={Boolean(errors.g80RegistrationNo)}
                    helperText={errors.g80RegistrationNo?.message}
                    size="small"
                    aria-describedby="validation-g80RegistrationNo"
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
            <FormControl fullWidth>
              <Controller
                name="address"
                control={control}
                render={({ field }) => (
                  <TextField
                    rows={4}
                    multiline
                    {...field}
                    type="text"
                    label="Address"
                    placeholder="Enter your address"
                    error={Boolean(errors.address)}
                    helperText={errors.address?.message}
                    size="small"
                    aria-describedby="validation-address"
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
            <FormControl fullWidth error={Boolean(errors.state)}>
              <Controller
                name="state"
                control={control}
                render={({ field }) => (
                  <SelectAutoComplete
                    id="state"
                    label="State"
                    nameArray={listOfStates}
                    value={locate}
                    onChange={(event) => {
                      field.onChange(event.target?.value);
                      handleLocationChange(event.target.value);
                    }}
                  />
                )}
              />
              {errors.state && (
                <FormHelperText sx={{ color: "error.main" }}>
                  {errors.state.message}
                </FormHelperText>
              )}
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          <FormControl fullWidth>
              <Controller
                name="pinCode"
                control={control}
                size="small"
                rules={{
                  required: false,
                  minLength: {
                    value: 6,
                    message: "Enter a 6 digit pincode",
                  },
                }}
                render={({ field: { value, onChange } }) => (
                  <TextField
                    value={value}
                    type="text"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    size="small"
                    maxLength={6}
                    label="Pin Code"
                    InputLabelProps={{ shrink: true }}
                    onChange={onChange}
                    onKeyDown={(event) => {
                      if (
                        !/^\d+$/.test(event.key) &&
                        event.key !== "Backspace" &&
                        event.key !== "Delete"
                      ) {
                        event.preventDefault();
                      }
                      if (
                        value &&
                        value.length === 6 &&
                        event.key !== "Backspace" &&
                        event.key !== "Delete"
                      ) {
                        event.preventDefault();
                      }
                    }}
                    placeholder="Enter Pin Code"
                    error={Boolean(errors.pinCode)}
                    helperText={errors.pinCode?.message}
                    aria-describedby="validation-basic-pin-code"
                  />
                )}
              />
            </FormControl>
          </Grid>
         
        </Grid>
      </CardContent>
  );
};

OrganisationDetails.propTypes = {
  formData: PropTypes.shape({
    societyDetails: PropTypes.object,
    organisationDetails: PropTypes.object
  }),
  onUpdate: PropTypes.func.isRequired
};

export default OrganisationDetails;
