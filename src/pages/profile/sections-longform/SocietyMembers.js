import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  TextField,
  Typography
} from "@mui/material";
import axios from "axios";
import PropTypes from "prop-types";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import SelectAutoCompleteRole from "src/@core/components/custom-components/SelectAutoCompleteRole";
import Icon from "src/@core/components/icon";
import authConfig from "src/configs/auth";
import { getUrl } from "src/helpers/utils";

const SocietyMembers = ({
  rowData,
  societyMembers,
  setSocietyMembers,
  open,
  onClose,
  setExistingList,
  setNewList,
  newList,
  rolesArray
}) => {

  const [roleId, setRoleId] = useState("");


  const {
    control,
    handleSubmit,
    setValue,
    reset,
    watch,
    formState: { errors },
  } = useForm();

  const handleCloseDialog = () => {
    onClose();
    reset();
    setRoleId(null);
    reset();
    setValue("name", "");
    setValue("mobileNumber", "");
    setValue("email", "");
    setValue("confirmEmail", "");
  };

  const email = watch("email");
  const confirmEmail = watch("confirmEmail");
  const emailsMatch = email === confirmEmail;

  useEffect(() => {
    setValue("name", rowData?.name);
    setValue("mobileNumber", rowData?.mobileNumber);
    setValue("email", rowData?.email);
    setValue("confirmEmail", rowData?.email);
    setRoleId(rowData?.roleId);
  }, [rowData]);

  const submit = (data) => {
    const fields = {
      id: crypto.randomUUID(),
      name: data?.name,
      mobileNumber: data?.mobileNumber,
      email: data?.email,
      isVerified: "PENDING",
      roleId: roleId,
    };
    setSocietyMembers((prevSocietyMembers) => [
      ...(prevSocietyMembers || []),
      fields,
    ]);
    setNewList((prevSocietyMembers) => [...(prevSocietyMembers || []), fields]);
    handleCloseDialog();
  };

  const update = (data) => {
    const updatedMembers = societyMembers?.map((member) =>
      member.id === rowData?.id
        ? {
            ...member,
            name: data?.name,
            mobileNumber: data?.mobileNumber,
            email: data?.email,
            isVerified: rowData?.isVerified,
            roleId: roleId,
          }
        : member
    );

    // Find the updated member
    const updatedObject = updatedMembers.find((member, index) => {
      const original = societyMembers[index];
      return (
        member.id === rowData?.id &&
        (member.name !== original.name ||
          member.mobileNumber !== original.mobileNumber ||
          member.email !== original.email ||
          member.isVerified !== original.isVerified ||
          member.roleId !== original.roleId)
      );
    });

    // If updatedObject is found and changed
    if (updatedObject) {
      const isInNewList = newList?.some((item) => item.id === updatedObject.id);

      if (isInNewList) {
        // Update newList
        const updatedNewList = newList.map((item) =>
          item.id === updatedObject.id ? updatedObject : item
        );
        setNewList(updatedNewList);
      } else {
        // Update existingList
        setExistingList((prev) => [...prev, updatedObject]);
      }
    }

    setSocietyMembers(updatedMembers);
    handleCloseDialog();
  };

  const handleRole = (newValue) => {
    setRoleId(newValue);
  };

  return (
      <Dialog open={open} onClose={handleCloseDialog} maxWidth="xs">
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            margin: "2px 2px 2px 2px",
          }}
          textAlign={"center"}
        >
          <Box
            sx={{
              marginLeft: 1.5,
            }}
          >
            Members
          </Box>
          <Box
            sx={{
              position: "absolute",
              top: "4px",
              right: "14px",
              marginRight: 2,
            }}
          >
            <IconButton
              size="small"
              onClick={handleCloseDialog}
              sx={{
                p: "0.2rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#d6d6f5",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Grid
            container
            alignItems="center"
            justifyContent="center"
            spacing={3}
            sx={{ marginBottom: 2 }}
          >
            <Grid item xs={12} sm={12}>
              <FormControl fullWidth>
                <Controller
                  name="name"
                  control={control}
                  rules={{ required: "Name is required" }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      value={field.value || ""}
                      label="Member Name"
                      InputLabelProps={{ shrink: true }}
                      size="small"
                      placeholder="Enter Member Name"
                      error={Boolean(errors.name)}
                      helperText={errors.name?.message}
                      aria-describedby="validation-basic-name"
                      inputProps={{ maxLength: 50 }}
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={12}>
              <FormControl fullWidth>
                <Controller
                  name="mobileNumber"
                  control={control}
                  rules={{ required: "Mobile Number is required" }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      value={field.value || ""}
                      type="tel"
                      label="Mobile Number"
                      size="small"
                      InputLabelProps={{ shrink: true }}
                      placeholder="1234567890"
                      inputProps={{
                        maxLength: 10,
                      }}
                      error={Boolean(errors.mobileNumber)}
                      helperText={errors.mobileNumber?.message}
                      aria-describedby="validation-basic-mobileNumber"
                      onKeyDown={(e) => {
                        if (
                          !/\d/.test(e.key) &&
                          e.key !== "Backspace" &&
                          e.key !== "ArrowLeft" &&
                          e.key !== "ArrowRight" &&
                          e.key !== "Delete" &&
                          e.key !== "Tab"
                        ) {
                          e.preventDefault();
                        }
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            {rowData?.isVerified === "VERIFIED" ? (
              <Grid item xs={12} sm={12}>
                <Typography>Email: {rowData?.email}</Typography>
              </Grid>
            ) : (
              <>
                <Grid item xs={12} sm={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="email"
                      control={control}
                      rules={{
                        required: "Email is required",
                        pattern: {
                          value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                          message: "Enter a valid email address",
                        },
                        minLength: {
                          value: 8,
                          message: "Email must be at least 8 characters long",
                        },
                        maxLength: {
                          value: 100,
                          message: "Email cannot be longer than 100 characters",
                        },
                        validate: async (value) => {
                          if (rowData?.email === value) {
                            return true; // Skip validation if email hasn't changed
                          }
                          try {
                            const res = await axios.post(
                              getUrl(authConfig.individualVerificationAudit) +
                                "/check-email",
                              { email: value }
                            );
                            if (res?.data?.message === "Email Already Exist") {
                              return "Email Already Exist";
                            }
                            return true;
                          } catch (err) {
                            console.error("Email validation failed:", err); 
                            return "Failed to validate email";
                          }
                        },
                      }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          value={field.value || ""}
                          label="Email"
                          InputLabelProps={{ shrink: true }}
                          size="small"
                          placeholder="Enter Email"
                          error={Boolean(errors.email)}
                          helperText={errors.email?.message}
                          aria-describedby="validation-basic-email"
                          inputProps={{ maxLength: 50 }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="confirmEmail"
                      control={control}
                      rules={{
                        required: "Email is required",
                        pattern: {
                          value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                          message: "Enter a valid email address",
                        },
                        minLength: {
                          value: 8,
                          message: "Email must be at least 8 characters long",
                        },
                        maxLength: {
                          value: 100,
                          message: "Email cannot be longer than 100 characters",
                        },
                      }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          value={field.value || ""}
                          label="Confirm Email"
                          InputLabelProps={{ shrink: true }}
                          size="small"
                          placeholder="please confirm your email. Email Can't be changed later"
                          error={Boolean(errors.confirmEmail)}
                          helperText={
                            errors.confirmEmail?.message ||
                            (email && confirmEmail && !emailsMatch
                              ? "Emails do not match"
                              : "")
                          }
                          aria-describedby="validation-basic-confirmEmail"
                          inputProps={{ maxLength: 50 }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
              </>
            )}

            <Grid item xs={12} sm={12}>
              <FormControl fullWidth error={Boolean(errors.roleId)}>
                <Controller
                  name="roleId"
                  control={control}
                  rules={{
                    required: rowData ? false : "Role id is required"
                  }}
                  render={({ field }) => (
                    <SelectAutoCompleteRole
                      id={"roleId"}
                      label={"Role"}
                      nameArray={rolesArray}
                      value={roleId}
                      onChange={(event) => {
                        field.onChange(event.target?.value);
                        handleRole(event.target.value)
                      }}
                    />
                  )}
                />
                {errors.roleId && (
                  <FormHelperText sx={{ color: "error.main" }}>
                    {errors.roleId.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button onClick={handleCloseDialog}>Cancel</Button>
          {rowData ? (
            <Button
              onClick={handleSubmit(update)}
              variant="contained"
              sx={{
                marginRight: 3.5,
              }}
              disabled={!emailsMatch}
            >
              Update
            </Button>
          ) : (
            <Button
              onClick={handleSubmit(submit)}
              variant="contained"
              sx={{
                marginRight: 3.5,
              }}
              disabled={!emailsMatch}
            >
              Add
            </Button>
          )}
        </DialogActions>
      </Dialog>
  );
};

SocietyMembers.propTypes = {
  rowData: PropTypes.object,
  societyMembers: PropTypes.array,
  setSocietyMembers: PropTypes.func.isRequired,
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  setExistingList: PropTypes.func.isRequired,
  setNewList: PropTypes.func.isRequired,
  newList: PropTypes.array,
  rolesArray: PropTypes.array
};

export default SocietyMembers;
