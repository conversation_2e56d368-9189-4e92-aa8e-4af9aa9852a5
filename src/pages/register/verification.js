import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  CircularProgress,
  Con<PERSON>er,
  <PERSON>rid,
  <PERSON>nack<PERSON>,
  Text<PERSON>ield,
  Typo<PERSON>
} from "@mui/material";
import PropTypes from "prop-types";
import { useEffect, useState } from "react";
// ** Layout Import
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
// ** Next Imports
import { useTheme } from "@mui/material/styles";
import { fetchIpAddress } from "src/@core/components/custom-components/FetchIpAddress";
import { useAuth } from "src/hooks/useAuth";
import OrganisationDetails from "./OrganisationDetails";

const Verification = ({
  email,
  setShowPwd,
  setShowDetails,
  showDetails,
  countdown,
  setCountdown,
}) => {
  const {
    control,
    formState: { errors },
  } = useForm();
  const auth = useAuth();
  const theme = useTheme();
  const [otp, setOtp] = useState("");

  const [loading, setLoading] = useState(false);
  const [showToast, setShowToast] = useState(false); // State for toast visibility
  const [toastMessage, setToastMessage] = useState("");

  useEffect(() => {
    if (countdown > 0) {
      const timerId = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timerId);
    }
  }, [countdown]);
  const handlePrev = () => {
    setShowPwd(false);
  };

  const handleFailure = () => {
    setToastMessage(
      "Failed to verify OTP. Please try again."
    );
    setShowToast(true);
  };

  const handleResendFailure = () =>{
    setToastMessage(
      "Failed to Resend OTP. Please try again."
    );
    setShowToast(true);
  }

  async function handleOTPVerification() {
    const data = {
      contactType: "EMAIL",
      contactValue: email,
      otp: otp,
    };

    try {
      const response = await auth.verifyOTP(data, handleFailure);
      if (response) {
        setShowDetails(true);
      }
    } catch (error) {
      console.error("Employee Creation failed:", error);
      handleFailure();
    }
  }

  async function handleResend() {
    setLoading(true);
    const ipAddress = await fetchIpAddress();
    const data = {
      contactType: "EMAIL",
      contactValue: email,
      ipAddress: ipAddress,
    };
    try {
      const response = await auth.resendOTP(data, handleResendFailure);
      if (response) {
        setCountdown(3);
      }
    } catch (error) {
      console.error("Employee Creation failed:", error);
      handleResendFailure();
    }
    setLoading(false);
  }
 
  const handleToastClose = () => {
    setShowToast(false);
    setToastMessage("");
  };

  return (
    <>
      {showDetails ? (
          <OrganisationDetails email={email} setShowPwd={setShowPwd} />
      ) : (
        <Container
          maxWidth="xs"
          sx={{
            height: "90vh",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "start",
            boxShadow: 3,
            p: 8,
            mt: 8,
            mb: 8,
            borderRadius: 6,
            bgcolor: "white",
            position: "relative",
            overflowY: "auto",
            flexGrow: 1,
          }}
        >
          <Box
            sx={{
              width: "80%",
              position: "absolute",
              mr: 7,
              "&:hover": {
              cursor: "pointer",
            },
            }}
          >
            <Icon
              icon="ooui:arrow-previous-ltr"
              fontSize={20}
              onClick={handlePrev}
            />
          </Box>
          <Box
            sx={{
              mt: 8,
            }}
          >
            <Grid
              container
              spacing={3}
              sx={{
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Grid item xs={8.5}>
                <Typography variant="h5" fontWeight={500} color="primary">
                  Verify Your Email
                </Typography>
              </Grid>
              <Grid item xs={8.5}>
                <Typography variant="body2" sx={{ my: 2 }}>
                  Please enter the 6-digit code sent to your email for
                  verification.
                  <br />
                  <Typography
                    sx={{
                      fontWeight: "bold",
                      fontSize: "0.8rem",
                      wordBreak: "break-word",
                    }}
                  >
                    {email}
                  </Typography>
                </Typography>
              </Grid>
              <Grid item xs={8.5} sx={{ marginBottom: 2 }}>
                <Controller
                  name="otp"
                  control={control}
                  rules={{
                    required: "OTP is required",
                    pattern: {
                      value: /^\d{6}$/,
                      message: "OTP must be a 6-digit number",
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      type="text"
                      size="small"
                      inputProps={{
                        inputMode: "numeric",
                        pattern: "[0-9]*",
                      }}
                      placeholder="OTP"
                      value={otp}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (/^\d*$/.test(value) && value.length <= 6) {
                          setOtp(value);
                        }
                      }}
                      InputLabelProps={{
                        shrink: true,
                        sx: { fontSize: "1rem" },
                      }}
                      error={Boolean(errors?.otp)}
                      helperText={errors?.otp?.message}
                      sx={{
                        borderRadius: "5px",
                        background: "white",
                        width: "100%",
                      }}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </Box>
          <Box
            sx={{
              width: "80%",
              py: 2,
              textAlign: "center",
              mt: "auto",
            }}
          >
            <>
              <Typography variant="body2" sx={{ mt: 2 }}>
                Didn't get an email ?{" "}
                {countdown > 0 ? (
                  <Typography
                    variant="body1"
                    sx={{
                      marginTop: "2px",
                      marginBottom: "10px",
                      color: "#a9a9a9",
                    }}
                  >
                    Resend OTP in: {countdown}s
                  </Typography>
                ) : (
                  <button
                    type="button"
                    style={{
                      color: theme.palette.primary.main,
                      cursor: "pointer",
                      border: "none",
                      background: "transparent",
                      padding: 0,
                      font: "inherit",
                      textDecoration: "underline"
                    }}
                    onClick={handleResend}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        handleResend();
                      }
                    }}
                  >
                    {loading ? (
                      <CircularProgress
                        color="inherit"
                        size={12}
                        sx={{ mt: 2 }}
                      />
                    ) : (
                      "Resend OTP"
                    )}
                  </button>
                )}
              </Typography>
              <Button
                variant="contained"
                color="primary"
                sx={{ mt: 4, width: "90%" }}
                onClick={handleOTPVerification}
                disabled={otp.length !== 6}
              >
                Continue
              </Button>
            </>
          </Box>
        </Container>
      )}
      <Snackbar
        open={showToast}
        autoHideDuration={5000} // Toast will be visible for 5 seconds
        onClose={handleToastClose}
        anchorOrigin={{ vertical: "top", horizontal: "right" }} // Position of the toast
      >
        <Alert
          onClose={handleToastClose}
          severity="error"
          sx={{
            color: "black",
            padding: "4px 8px", // Reduce padding to make it smaller
            fontSize: "0.875rem", // Adjust font size for a more compact look
            borderRadius: "12px", // Optional: you can adjust the border radius
            border: "0.5px solid #ccc", // Optional: set a border or remove it completely
          }}
        >
          {toastMessage}
        </Alert>
      </Snackbar>
    </>
  );
};

Verification.propTypes = {
  email: PropTypes.string.isRequired,
  setShowPwd: PropTypes.func.isRequired,
  setShowDetails: PropTypes.func.isRequired,
  showDetails: PropTypes.bool.isRequired,
  countdown: PropTypes.number.isRequired,
  setCountdown: PropTypes.func.isRequired
};

export default Verification;
