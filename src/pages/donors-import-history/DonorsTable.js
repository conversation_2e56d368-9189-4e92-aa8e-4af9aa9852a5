import {
  <PERSON><PERSON>,
  <PERSON>,
  Button, Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Paper,
  Snackbar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField
} from "@mui/material";
import PropTypes from "prop-types";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import Icon from "src/@core/components/icon";
import authConfig from "src/configs/auth";
import CustomAvatar from "src/@core/components/mui/avatar";
import CustomChip from "src/@core/components/mui/chip";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";

const userStatusObj = {
  DUPLICATED: "Duplicate",
  PENDING: "Pending",
};

const DonorsImportTable = ({ open, onClose, donorsList, rowData }) => {
  const { setStagingDonorId, stagingDonorId } = useContext(AuthContext);
  const [editedRows, setEditedRows] = useState({});
  const [editingRowIndex, setEditingRowIndex] = useState(null);
  const [confirmDeleteDialogOpen, setConfirmDeleteDialogOpen] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [severity, setSeverity] = useState("success");
  const [validationErrors, setValidationErrors] = useState({});
  const [currentRow, setCurrentRow] = useState();

  const handleToastClose = () => {
    setShowToast(false);
    setToastMessage("");
  };

  useEffect(() => {
    if (donorsList && donorsList.length > 0) {
      const initialValidationErrors = {};

      // Validate all fields for all rows
      donorsList.forEach((row) => {
        const duplicatedFields = row.duplicatedFields || [];

        displayFields.forEach((field) => {
          const fieldKey = field.field;
          let value;

          if (field.getValue) {
            value = field.getValue(row);
          } else {
            value = row[fieldKey] || "";
          }

          const error = validateField(fieldKey, value, duplicatedFields);
          if (error) {
            initialValidationErrors[`${row.id}-${fieldKey}`] = error;
          }
        });
      });

      setValidationErrors(initialValidationErrors);
    }
  }, [donorsList]);

  const handleDelete = () => {
    setConfirmDeleteDialogOpen(false);
    axios({
      method: "delete",
      url:
        getUrl(authConfig.donorImportEndpoint) +
        "/deactivate-import-staging/" +
        currentRow?.id,
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setToastMessage("Record deleted successfully!");
        setSeverity("success");
        setShowToast(true);
        setStagingDonorId({
          ...stagingDonorId,
          id: rowData?.id,
        });
      })
      .catch((err) => {
        setToastMessage("Error deleting record");
        setSeverity("error");
        setShowToast(true);
      });
  };

  const handleSaveClick = (data) => {
    // Get the edited values for this row
    const editedData = editedRows[data.id] || {};
    
    // Check for validation errors, but ignore duplicate errors for changed fields
    let hasErrors = false;
    const relevantErrors = [];
    
    Object.keys(validationErrors).forEach(key => {
      if (key.startsWith(`${data.id}-`)) {
        const fieldKey = key.split('-')[1];
        
        // Get original and current values
        let originalValue;
        if (fieldKey === "email") {
          originalValue = data.email;
        } else if (fieldKey === "panNo") {
          originalValue = data.panNo;
        } else if (fieldKey === "mobileNumber") {
          originalValue = data.mobileNumber;
        } else if (fieldKey === "state" || fieldKey === "pinCode" || fieldKey === "address") {
          originalValue = data.donorMetaData?.[fieldKey];
        } else {
          originalValue = data[fieldKey];
        }
        
        let currentValue;
        if (fieldKey === "state" || fieldKey === "pinCode" || fieldKey === "address") {
          currentValue = editedData.donorMetaData?.[fieldKey] || originalValue;
        } else {
          currentValue = editedData[fieldKey] || originalValue;
        }
        
        // If the value has changed, ignore duplicate errors
        const valueChanged = currentValue !== originalValue;
        const isDuplicateError = validationErrors[key]?.includes('already exists');
        
        if (!(valueChanged && isDuplicateError) && validationErrors[key]) {
          hasErrors = true;
          relevantErrors.push(validationErrors[key]);
        }
      }
    });
    
    if (hasErrors) {
      setToastMessage(relevantErrors[0] || "Please fix validation errors");
      setSeverity("error");
      setShowToast(true);
      return;
    }
    
    // Prepare data for API call
    const fields = {
      name: editedData.name || data.name,
      email: editedData.email || data.email,
      mobileNumber: editedData.mobileNumber || data.mobileNumber,
      panNo: editedData.panNo ? editedData.panNo.toUpperCase() : data.panNo?.toUpperCase(),
      donorMetaData: {
        state: editedData.donorMetaData?.state || data.donorMetaData?.state,
        pinCode: editedData.donorMetaData?.pinCode || data.donorMetaData?.pinCode,
        address: editedData.donorMetaData?.address || data.donorMetaData?.address,
      }
    };

    axios({
      method: "patch",
      url:
        getUrl(authConfig.donorImportEndpoint) +
        "/patch-import-staging/" +
        data?.id,
      headers: getAuthorizationHeaders(),
      data: fields,
    })
      .then((res) => {
        // Clear all validation errors for this row
        const clearedValidationErrors = { ...validationErrors };
        Object.keys(clearedValidationErrors).forEach(key => {
          if (key.startsWith(`${data.id}-`)) {
            delete clearedValidationErrors[key];
          }
        });
        
        setValidationErrors(clearedValidationErrors);
        setEditedRows((prevState) => {
          const newState = { ...prevState };
          delete newState[data.id];
          return newState;
        });
        
        setToastMessage("Record Updated successfully!");
        setSeverity("success");
        setShowToast(true);
        setStagingDonorId({
          ...stagingDonorId,
          id: rowData?.id,
        });
        setEditingRowIndex(null);
        
      })
      .catch((err) => {
        setToastMessage("Error updating record");
        setSeverity("error");
        setShowToast(true);
      });
  };

  const handleDialogClose = () => {
    setValidationErrors({});
    setEditedRows({});
    setEditingRowIndex(null);
    onClose();
  };

  // Helper functions for individual field validations
  const validateEmail = (value) => {
    if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) {
      return "Invalid email format";
    }
    return null;
  };

  const validateMobileNumber = (value) => {
    if (!/^\d{10}$/.test(value)) {
      return "Invalid mobile number";
    }
    return null;
  };

  const validatePanNo = (value) => {
    const panRegex = /^[A-Z]{5}\d{4}[A-Z]$/;
    if (!panRegex.test(value)) {
      return "Invalid PAN format";
    }
    return null;
  };

  const validatePinCode = (value) => {
    if (!/^\d{6}$/.test(value)) {
      return "Invalid PIN code";
    }
    return null;
  };

  const validateFieldFormat = (field, value) => {
    if (!value) return null;

    const validators = {
      email: validateEmail,
      mobileNumber: validateMobileNumber,
      panNo: validatePanNo,
      pinCode: validatePinCode,
    };

    const validator = validators[field];
    return validator ? validator(value) : null;
  };

  const validateField = (field, value, duplicatedFields = []) => {
    // If the field is in duplicatedFields, show duplicate error
    if (duplicatedFields.includes(field)) {
      const fieldName = (() => {
        if (field === 'email') return 'Email';
        if (field === 'panNo') return 'PAN No';
        return field;
      })();
      return `${fieldName} already exists`;
    }
    
    // Otherwise, validate format
    return validateFieldFormat(field, value);
  };

  // Helper function to get original field value
  const getOriginalValue = (row, field) => {
    const metaDataFields = ["state", "pinCode", "address"];
    if (metaDataFields.includes(field)) {
      return row.donorMetaData?.[field];
    }
    return row[field];
  };

  // Helper function to get field display name
  const getFieldDisplayName = (field) => {
    const fieldNames = {
      email: 'Email',
      panNo: 'PAN No'
    };
    return fieldNames[field] || field;
  };

  // Helper function to validate field with duplication check
  const validateFieldWithDuplication = (field, value, originalValue, duplicatedFields) => {
    if (value !== originalValue) {
      return validateFieldFormat(field, value);
    }
    
    if (duplicatedFields.includes(field)) {
      const fieldName = getFieldDisplayName(field);
      return `${fieldName} already exists`;
    }
    
    return validateFieldFormat(field, value);
  };

  // Helper function to update edited rows state
  const updateEditedRowsState = (rowId, field, value) => {
    const metaDataFields = ["state", "pinCode", "address"];
    
    if (metaDataFields.includes(field)) {
      setEditedRows((prevState) => ({
        ...prevState,
        [rowId]: {
          ...(prevState[rowId] || {}),
          donorMetaData: {
            ...(prevState[rowId]?.donorMetaData || {}),
            [field]: value,
          },
        },
      }));
    } else {
      setEditedRows((prevState) => ({
        ...prevState,
        [rowId]: {
          ...(prevState[rowId] || {}),
          [field]: value,
        },
      }));
    }
  };

  const handleFieldChange = (rowId, field, value) => {
    const row = donorsList.find((r) => r.id === rowId);
    const duplicatedFields = row?.duplicatedFields || [];
    const originalValue = getOriginalValue(row, field);
    
    const error = validateFieldWithDuplication(field, value, originalValue, duplicatedFields);
    
    setValidationErrors((prev) => ({
      ...prev,
      [`${rowId}-${field}`]: error,
    }));

    updateEditedRowsState(rowId, field, value);
  };

  const handleEditRow = (rowId) => {
    if (editingRowIndex === rowId) {
      // If already editing this row, save changes
      const rowData = donorsList.find((row) => row.id === rowId);
      if (rowData) {
        const updatedData = {
          ...rowData,
          ...editedRows[rowId],
          donorMetaData: {
            ...(rowData.donorMetaData || {}),
            ...(editedRows[rowId]?.donorMetaData || {}),
          },
        };
        handleSaveClick(updatedData);
      }
    } else {
      // Start editing this row
      setEditingRowIndex(rowId);
    }
  };

  const mapIsActiveToLabel = (status) => {
    return userStatusObj[status];
  };

  const getStatusColor = (status) => {
    return status === "PENDING" ? "warning" : "error";
  };

  const getRowStyle = (status) => {
    return status === "DUPLICATED"
      ? { backgroundColor: "rgba(255, 0, 0, 0.1)" }
      : {};
  };

  // Helper function to get current field value
  const getCurrentValue = (row, field, fieldKey) => {
    let currentValue;
    if (field.getValue) {
      // For nested fields with custom getter
      currentValue = field.getValue(row);
      // Check if there's an edited value
      if (editedRows[row.id]?.donorMetaData?.[fieldKey]) {
        currentValue = editedRows[row.id].donorMetaData[fieldKey];
      }
    } else {
      // For regular fields
      currentValue = row[fieldKey] || "";
      // Check if there's an edited value
      if (editedRows[row.id]?.[fieldKey]) {
        currentValue = editedRows[row.id][fieldKey];
      }
    }
    return currentValue;
  };

  // Helper function to get input props for TextField
  const getInputProps = (fieldKey) => ({
    maxLength: fieldKey === "panNo" ? 10 : undefined,
    style: {
      textTransform: fieldKey === "panNo" ? "uppercase" : "none",
    },
  });

  // Helper function to get TextField styles
  const getTextFieldStyles = (hasError) => ({
    "& .MuiOutlinedInput-root": {
      minHeight: "32px",
      "& input": {
        padding: "6px 8px",
        fontSize: "0.75rem",
      },
      ...(hasError && {
        "& fieldset": {
          borderColor: "error.main",
        },
        "&:hover fieldset": {
          borderColor: "error.dark",
        },
      }),
    },
    "& .MuiFormHelperText-root": {
      margin: 0,
      fontSize: "0.625rem",
    },
  });

  // Helper function to render editing field
  const renderEditingField = (row, field, fieldKey, currentValue, hasError, errorKey) => (
    <TextField
      fullWidth
      variant="outlined"
      size="small"
      value={currentValue}
      onChange={(e) => {
        const newValue = e.target.value;
        handleFieldChange(row.id, fieldKey, newValue);
      }}
      error={hasError}
      helperText={hasError ? validationErrors[errorKey] : ""}
      inputProps={getInputProps(fieldKey)}
      sx={getTextFieldStyles(hasError)}
    />
  );

  // Helper function to render display field
  const renderDisplayField = (row, fieldKey, currentValue, hasError, errorKey) => (
    <Box
      sx={{
        position: "relative",
        ...(row.duplicatedFields?.includes(fieldKey) && {
          color: "error.main",
        }),
      }}
    >
      {currentValue}
      {hasError && (
        <Box
          sx={{
            color: "error.main",
            fontSize: "0.625rem",
            position: "absolute",
            bottom: -10,
          }}
        >
          {validationErrors[errorKey]}
        </Box>
      )}
    </Box>
  );

  // Define the fields to display in the table
  const displayFields = [
    { field: "name", label: "Name" },
    { field: "email", label: "Email" },
    { field: "mobileNumber", label: "Mobile Number" },
    { field: "panNo", label: "Pan No" },
    {
      field: "state",
      label: "State",
      getValue: (row) => row.donorMetaData?.state || "",
    },
    {
      field: "pinCode",
      label: "Pin Code",
      getValue: (row) => row.donorMetaData?.pinCode || "",
    },
    {
      field: "address",
      label: "Address",
      getValue: (row) => row.donorMetaData?.address || "",
    },
  ];

  return (
    <>
      <Dialog open={open} onClose={handleDialogClose} fullScreen>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "flex-start", md: "flex-start" },
            fontSize: { xs: 20, md: 26 },
            height: "45px",
          }}
        >
          Donors List
          <Box
            sx={{
              position: "absolute",
              top: "9px",
              right: "10px",
              mr: { xs: 5.5, sm: 5.5, md: 5.5, lg: 5.5, xl: 5.5 },
            }}
          >
            <IconButton
              size="small"
              onClick={handleDialogClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#9a9ae5",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 1 }}>
          <TableContainer
            component={Paper}
            sx={{ height: "80vh", width: "100%" }}
          >
            <Table stickyHeader size="small">
              <TableHead>
                <TableRow>
                  {displayFields.map((field) => (
                    <TableCell
                      key={field.field}
                      sx={{
                        whiteSpace: "nowrap",
                        letterSpacing: "0.01em",
                        fontSize: "0.8125rem",
                        textTransform: "uppercase",
                      }}
                    >
                      {field.label}
                    </TableCell>
                  ))}
                  <TableCell
                    sx={{
                      whiteSpace: "nowrap",
                      letterSpacing: "0.01em",
                      fontSize: "0.8125rem",
                      textTransform: "uppercase",
                    }}
                  >
                    Status
                  </TableCell>
                  <TableCell
                    sx={{
                      whiteSpace: "nowrap",
                      letterSpacing: "0.01em",
                      fontSize: "0.8125rem",
                      textTransform: "uppercase",
                      textAlign: "center",
                    }}
                  >
                    Actions
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {donorsList &&
                  donorsList.map((row) => {
                    const isEditing = editingRowIndex === row.id;

                    return (
                      <TableRow key={row.id} sx={getRowStyle(row.status)}>
                        {displayFields.map((field) => {
                          const fieldKey = field.field;
                          const errorKey = `${row.id}-${fieldKey}`;
                          const hasError = validationErrors[errorKey] != null;
                          const currentValue = getCurrentValue(row, field, fieldKey);

                          return (
                            <TableCell key={`${row.id}-${fieldKey}`}>
                              {isEditing 
                                ? renderEditingField(row, field, fieldKey, currentValue, hasError, errorKey)
                                : renderDisplayField(row, fieldKey, currentValue, hasError, errorKey)
                              }
                            </TableCell>
                          );
                        })}
                        <TableCell>
                          <CustomChip
                            rounded={true}
                            skin="light"
                            size="small"
                            label={mapIsActiveToLabel(row.status)}
                            color={getStatusColor(row.status)}
                            sx={{ textTransform: "capitalize" }}
                          />
                        </TableCell>
                        <TableCell>
                          <Box
                            sx={{
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                            }}
                          >
                            <CustomAvatar
                              skin="light"
                              variant="rounded"
                              sx={{
                                mr: { xs: 2, lg: 4 },
                                width: 34,
                                height: 34,
                                cursor: "pointer",
                              }}
                              onClick={() => handleEditRow(row.id)}
                            >
                              <Icon
                                icon={
                                  isEditing
                                    ? "mdi:content-save"
                                    : "iconamoon:edit"
                                }
                                fontSize="1.25rem"
                              />
                            </CustomAvatar>
                            <CustomAvatar
                              skin="light"
                              variant="rounded"
                              sx={{
                                mr: { xs: 2, lg: 4 },
                                width: 34,
                                height: 34,
                                cursor: "pointer",
                              }}
                              onClick={() => {
                                setCurrentRow(row);
                                setConfirmDeleteDialogOpen(true);
                              }}
                            >
                              <Icon icon="fluent:delete-32-filled" />
                            </CustomAvatar>
                          </Box>
                        </TableCell>
                      </TableRow>
                    );
                  })}
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
        <DialogActions
          sx={{
            p: 2,
            borderTop: "1px solid #eee",
            display: "flex",
            justifyContent: "flex-end",
            "& .MuiButton-root": {
              height: 40,
              marginLeft: 1,
            },
          }}
        >
          <Button onClick={handleDialogClose} color="inherit">
            Cancel
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={confirmDeleteDialogOpen}
        onClose={() => setConfirmDeleteDialogOpen(false)}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div
                dangerouslySetInnerHTML={{
                  __html: `<h4>Are you sure you want to delete record?</h4>`,
                }}
              />
            </DialogContentText>
          </DialogContent>

          <DialogActions>
            <Button onClick={handleDelete} variant="contained">
              Yes
            </Button>
            <Button onClick={() => setConfirmDeleteDialogOpen(false)}>
              No
            </Button>
          </DialogActions>
        </Box>
      </Dialog>

      <Snackbar
        open={showToast}
        autoHideDuration={2000}
        onClose={handleToastClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleToastClose}
          severity={severity}
          sx={{
            color: "black",
            padding: "4px 8px",
            fontSize: "0.875rem",
            borderRadius: "2px",
            border: "0.5px solid #ccc",
          }}
        >
          {toastMessage}
        </Alert>
      </Snackbar>
    </>
  );
};

DonorsImportTable.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  donorsList: PropTypes.array.isRequired,
  rowData: PropTypes.object
};

export default DonorsImportTable;




