import PropTypes from "prop-types";
import {
  Alert,
  Box,
  Button,
  FormControl,
  FormHelperText,
  Grid,
  Snackbar,
  TextField,
  Typography,
} from "@mui/material";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";

const EditProfileForm = ({
  profileData,
  setIsEditing,
  fetchOrganisationData,
}) => {
  const {
    handleSubmit,
    setValue,
    control,
    formState: { errors },
  } = useForm();
  const { user, listValues, getAllListValuesByListNameId } =
    useContext(AuthContext);

  const [donorTypeOptionsData, setDonorTypeOptionsData] = useState([]);
  const [selectedDonorTypeId, setSelectedDonorTypeId] = useState(null);
  const [referralSourceOptionsData, setReferralSourceOptionsData] = useState(
    []
  );

  const panRegex = /^[A-Z]{3}[PCHFATBLJG][A-Z]\d{4}[A-Z]$/;
  const validFourthChars = ["P", "C", "H", "F", "A", "T", "B", "L", "J", "G"];
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [selectedReferralSourceId, setSelectedReferralSourceId] =
    useState(null);

  const handleReferralSourceChange = (event) => {
    const selectedId = event.target.value;
    setSelectedReferralSourceId(selectedId);
    const referralSourceRegistered = referralSourceOptionsData.find(
      (referralRegistered) => referralRegistered?.value === selectedId
    );

    if (referralSourceRegistered?.key === "Any Other") {
      setValue("donorReferralSourceAnyOther", ""); // update form value
    } else {
      setValue("donorReferralSourceAnyOther", null); // reset form value
    }
  };

  const handleError = (error) => {
    console.error("Basic profile: All Services:", error);
  };

  useEffect(() => {
    getAllListValuesByListNameId(
      authConfig.donorTypeListNameId, // use your actual key here
      (data) =>
        setDonorTypeOptionsData(
          data?.listValues?.map((item) => ({
            value: item.id,
            key: item.listValue,
          }))
        ),
      handleError
    );
    getAllListValuesByListNameId(
      authConfig.referralSourceListNameId, // use your actual key here
      (data) =>
        setReferralSourceOptionsData(
          data?.listValues?.map((item) => ({
            value: item.id,
            key: item.listValue,
          }))
        ),
      handleError
    );
  }, []);

  const showSnackbar = (message, severity = "success") => {
    setSnackbar({
      open: true,
      message,
      severity,
    });
  };

  const handleCloseSnackbar = () => {
    setSnackbar((prev) => ({ ...prev, open: false }));
  };

  async function submit(data) {
    const payload = Object.entries(data).reduce((acc, [key, value]) => {
      if (value != null) acc[key] = value; // filters out null and undefined
      return acc;
    }, {});
    payload.donorType = selectedDonorTypeId;
    payload.donorReferralSource = selectedReferralSourceId;
    payload.roleId = authConfig.donorRoleId;

    const url =
      getUrl(authConfig.organisationsEndpoint) +
      "/update-individual/" +
      user?.orgId;

    axios({
      method: "patch",
      url: url,
      headers: getAuthorizationHeaders(),
      data: payload,
    })
      .then((res) => {
        setIsEditing(false);
        showSnackbar("Profile updated successfully");
        fetchOrganisationData();
      })
      .catch((err) => showSnackbar("Error updating profile", "error"));
  }

  useEffect(() => {
    setValue("name", profileData?.name);
    setValue("email", profileData?.email);
    setValue("contactNumber", profileData?.contactNumber);
    setSelectedDonorTypeId(profileData?.donorType);
    setValue("donorOrgName", profileData?.donorOrgName);
    setValue("panNo", profileData?.panNo);
    setSelectedReferralSourceId(profileData?.donorReferralSource);
    if (
      listValues?.find((item) => item.id === profileData?.donorReferralSource)
        ?.name === "Any Other"
    ) {
      setValue(
        "donorReferralSourceAnyOther",
        profileData?.donorReferralSourceAnyOther
      );
    }
    setValue("address", profileData?.address);
    setValue("state", profileData?.state);
    setValue("pinCode", profileData?.pinCode);
  }, [profileData]);

  return (
    <>
      <Grid container spacing={2}>
        {/* Name */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <Controller
              name="name"
              control={control}
              rules={{ required: "Name is required" }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Name"
                  fullWidth
                  size="small"
                  error={Boolean(errors.name)}
                  helperText={errors.name?.message}
                  placeholder="Enter your full name"
                  aria-describedby="name"
                />
              )}
            />
          </FormControl>
        </Grid>

        {/* Email */}
        <Grid container item xs={12} md={6} spacing={2}>
          <Grid item>
            <Typography className="data-field"> Email:</Typography>
          </Grid>
          <Grid item>
            <Typography style={{ fontWeight: "bold" }}>
              {profileData?.email}
            </Typography>
          </Grid>
        </Grid>

        {/* Mobile Number */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <Controller
              name="contactNumber"
              control={control}
              rules={{
                required: "Mobile number is required",
                pattern: {
                  value: /^[6-9]\d{9}$/,
                  message:
                    "Please enter a valid 10-digit mobile number starting with 6-9",
                },
                validate: {
                  validLength: (value) =>
                    value.length === 10 || "Mobile number must be 10 digits",
                  onlyNumbers: (value) =>
                    /^\d+$/.test(value) ||
                    "Mobile number should contain only numbers",
                },
              }}
              render={({ field }) => (
                <MobileNumberValidation
                  {...field}
                  label="Mobile Number"
                  fullWidth
                  size="small"
                  error={Boolean(errors.contactNumber)}
                  helperText={errors.contactNumber?.message}
                  placeholder="Enter your mobile number"
                  inputProps={{
                    maxLength: 10,
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                  }}
                  aria-describedby="contactNumber"
                />
              )}
            />
          </FormControl>
        </Grid>

        {/* Donor Type */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth error={Boolean(errors.donorType)}>
            <Controller
              name="donorType"
              control={control}
              rules={{
                required: selectedDonorTypeId
                  ? false
                  : "Donor Type is required",
              }}
              render={({ field }) => (
                <SelectAutoComplete
                  size="small"
                  labelId="donor-type-label"
                  label="Donor Type"
                  nameArray={donorTypeOptionsData}
                  value={selectedDonorTypeId}
                  error={Boolean(errors.donorType)}
                  onChange={(e) => {
                    field.onChange(e.target.value);
                    setSelectedDonorTypeId(e.target.value);
                  }}
                />
              )}
            />
            {errors.donorType && (
              <FormHelperText sx={{ color: "error.main" }}>
                {errors.donorType.message}
              </FormHelperText>
            )}
          </FormControl>
        </Grid>
        {listValues?.find((item) => item.id === selectedDonorTypeId)?.name ===
          "Entity" && (
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <Controller
                name="donorOrgName"
                control={control}
                rules={{
                  required: "Organization Name is required",
                  maxLength: {
                    value: 100,
                    message: "Organization Name cannot exceed 50 characters",
                  },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Organization Name*"
                    placeholder="Enter your organization name"
                    fullWidth
                    size="small"
                    error={Boolean(errors.donorOrgName)}
                    helperText={errors.donorOrgName?.message}
                    inputProps={{ maxLength: 100 }}
                  />
                )}
              />
            </FormControl>
          </Grid>
        )}

        {/* PAN Number */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <Controller
              name="panNo"
              control={control}
              rules={{
                required: false,
                validate: (value) => {
                  if (!value) return "";
                  if (!panRegex.test(value)) {
                    return "Invalid PAN No. Must be 5 letters, 4 digits, 1 letter (e.g., **********)";
                  }
                  if (!validFourthChars.includes(value.charAt(3))) {
                    return "Invalid 4th character. Must be one of: P, C, H, F, A, T, B, L, J, G";
                  }
                  return "";
                },
              }}
              render={({ field }) => (
                <TextField
                  {...field}
                  onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                  size="small"
                  label="PAN Number"
                  placeholder="Enter your PAN Number"
                  error={Boolean(errors.panNo)}
                  helperText={errors.panNo?.message}
                  inputProps={{ maxLength: 10 }}
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControl fullWidth error={Boolean(errors.referralSource)}>
            <Controller
              name="referralSource"
              control={control}
              rules={{
                required: selectedReferralSourceId
                  ? false
                  : "Referral Source is required",
              }}
              render={({ field }) => (
                <SelectAutoComplete
                  size="small"
                  labelId="referral-source-label"
                  label="Referral Source"
                  nameArray={referralSourceOptionsData}
                  value={selectedReferralSourceId}
                  error={Boolean(errors.referralSource)}
                  helperText={errors.referralSource?.message}
                  onChange={(e) => {
                    field.onChange(e.target.value);
                    handleReferralSourceChange(e);
                  }}
                />
              )}
            />
            {errors.referralSource && (
              <FormHelperText sx={{ color: "error.main" }}>
                {errors.referralSource.message}
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        {listValues?.find((item) => item.id === selectedReferralSourceId)
          ?.name === "Any Other" && (
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <Controller
                name="donorReferralSourceAnyOther"
                control={control}
                rules={{
                  required: "Referral Source is required",
                  maxLength: {
                    value: 100,
                    message: "Referral Source cannot exceed 50 characters",
                  },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Any Other Referral Source*"
                    placeholder="Enter Any Other Referral Source"
                    fullWidth
                    size="small"
                    error={Boolean(errors.donorReferralSourceAnyOther)}
                    helperText={errors.donorReferralSourceAnyOther?.message}
                    inputProps={{ maxLength: 100 }}
                  />
                )}
              />
            </FormControl>
          </Grid>
        )}

        {/* Address */}
        <Grid item xs={12}>
          <FormControl fullWidth>
            <Controller
              name="address"
              control={control}
              rules={{ required: "Address is required" }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Address"
                  fullWidth
                  size="small"
                  multiline
                  rows={3}
                  error={Boolean(errors.address)}
                  helperText={errors.address?.message}
                  placeholder="Enter your complete address"
                />
              )}
            />
          </FormControl>
        </Grid>

        {/* State */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <Controller
              name="state"
              control={control}
              rules={{ required: "State is required" }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="State"
                  fullWidth
                  size="small"
                  error={Boolean(errors.state)}
                  helperText={errors.state?.message}
                  placeholder="Enter your state"
                />
              )}
            />
          </FormControl>
        </Grid>

        {/* Pin Code */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <Controller
              name="pinCode"
              control={control}
              rules={{
                required: "PIN code is required",
                pattern: {
                  value: /^[1-9]\d{5}$/,
                  message: "Please enter a valid 6-digit PIN code",
                },
                validate: {
                  validLength: (value) =>
                    value.length === 6 || "PIN code must be 6 digits",
                  onlyNumbers: (value) =>
                    /^\d+$/.test(value) ||
                    "PIN code should contain only numbers",
                  validFirstDigit: (value) =>
                    value[0] !== "0" || "PIN code cannot start with 0",
                },
              }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="PIN Code"
                  fullWidth
                  size="small"
                  error={Boolean(errors.pinCode)}
                  helperText={errors.pinCode?.message}
                  placeholder="Enter your PIN code"
                  inputProps={{
                    maxLength: 6,
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                  }}
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <Box
            sx={{ display: "flex", justifyContent: "flex-end", gap: 2, mt: 2 }}
          >
            <Button variant="outlined" onClick={() => setIsEditing(false)}>
              Cancel
            </Button>
            <Button
              type="button"
              variant="contained"
              onClick={handleSubmit(submit)}
            >
              Save
            </Button>
          </Box>
        </Grid>
      </Grid>
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{
            width: "100%",
            color: "#fff",
            backgroundColor:
              snackbar.severity === "success" ? "#4caf50" : undefined,
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
};

EditProfileForm.propTypes = {
  profileData: PropTypes.object,
  setIsEditing: PropTypes.func.isRequired,
  fetchOrganisationData: PropTypes.func.isRequired
};

export default EditProfileForm;
