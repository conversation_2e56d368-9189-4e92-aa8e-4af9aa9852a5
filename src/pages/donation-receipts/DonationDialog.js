// src/components/DonationDialog.js

import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  TextField,
  Typography,
} from "@mui/material";
import PropTypes from "prop-types";
import { useContext, useEffect, useState } from "react";
import { Controller } from "react-hook-form";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import { useAuth } from "src/hooks/useAuth";

const DonationDialog = ({
  open,
  onClose,
  formData,
  control,
  errors,
  setValue,
  handleSubmit,
  donationTypesData,
  paymentModeData,
  paymentTypeData,
  tenantsList,
  donorsList,
  fetchReceipts,
  page,
  pageSize,
  searchKeyword,
  donationHeadsList,
  tenantId,
  setTenantId,
  donorId,
  setDonorId,
  donorData,
}) => {
  const auth = useAuth();
  const [donationType, setDonationType] = useState(null);
  const { user, listValues } = useContext(AuthContext);
  const [payment, setPayment] = useState(null);
  const [paymentType, setPaymentType] = useState(null);

  // Helper function to check if form is in edit mode
  const isEditMode = () => {
    return formData && Object.keys(formData).length > 0;
  };

  // Helper function to get dialog title
  const getDialogTitle = () => {
    return isEditMode() ? "Update New Receipt" : "Add New Receipt";
  };

  // Helper function to get button text
  const getButtonText = () => {
    return isEditMode() ? "Update Receipt" : "Save Receipt";
  };

  // Helper function to determine org ID
  const getOrgId = () => {
    return user?.organisationCategory === "SUPER_ADMIN" ? tenantId : user?.orgId;
  };

  // Helper function to determine donor ID from form data
  const getDonorIdFromFormData = () => {
    return formData?.tenantDonorsDTO?.id || formData?.selfRegisteredDonorDTO?.id;
  };

  // Helper function for validation rules
  const getValidationRule = (fieldName, fieldValue) => {
    if (!isEditMode()) {
      return `${fieldName} is required`;
    }
    return fieldValue ? "" : `${fieldName} is required`;
  };



  useEffect(() => {
    setTenantId(formData?.orgId);
    setDonorId(getDonorIdFromFormData());
    setPayment(formData?.metaData?.paymentMode);
    setPaymentType(formData?.metaData?.paymentType);
    setValue("amount", formData?.metaData?.amount);
    setValue("paymentDetails", formData?.metaData?.paymentDetails);
    setValue("reference", formData?.metaData?.reference);
    setValue("additionalNotes", formData?.metaData?.additionalNotes);
    setValue("donationDate", formData?.receiptDate);
    setDonation(formData?.donationHeadId);
    setDonationType(formData?.donationTypeId);
  }, [formData]);

  const [donation, setDonation] = useState(null);
  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const handleClose = () => setOpenDialogContent(false);

  const handleDialogClose = () => {
    setDonationType("")
    setPayment("")
    setPaymentType("")
    onClose()
  }

  const handleDonationChange = (newValue) => {
    setDonation(newValue);
  };

  const handleSuccess = () => {
    const message = `
        <div> 
          <h3> Receipt added Successfully.</h3>
        </div>
      `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = (err) => {
    let message;
    if (err?.response?.status == 400) {
      message = `
        <div>
          <h3>Receipt already exists!</h3>
        </div>
      `;
    } else {
      message = `
        <div> 
          <h3> Failed to Add Receipt. Please try again later.</h3>
        </div>
      `;
    }

    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleSuccessUpdate = () => {
    const message = `
        <div> 
          <h3> Receipt updated Successfully.</h3>
        </div>
      `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailureUpdate = (err) => {
    let message;
    if (err?.response?.status == 400) {
      message = `
        <div>
          <h3>Receipt already exists!</h3>
        </div>
      `;
    } else {
      message = `
        <div> 
          <h3> Failed to update Receipt. Please try again later.</h3>
        </div>
      `;
    }

    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  async function submit(data) {
    const fields = {
      orgId: getOrgId(),
      donorId:donorId,
      metaData: {
        amount: data?.amount,
        paymentMode: payment,
        paymentType:paymentType,
        paymentDetails: data?.paymentDetails,
        reference: data?.reference,
        additionalNotes: data?.additionalNotes,
      },
      donationTypeId: donationType,
      donationHeadId: donation,
      receiptDate: data?.donationDate,
    };
    try {
      await auth.postDonationReceipt(
        fields,
        handleFailure,
        handleSuccess
      );
    } catch (error) {
      console.error("Employee Creation failed:", error);
      handleFailure();
    }
    fetchReceipts(page, pageSize, searchKeyword);
    handleDialogClose();
  }

  async function update(data) {
    const fields = {
      orgId: getOrgId(),
      donorId:donorId,
      metaData: {
        amount: data?.amount,
        paymentType:paymentType,
        paymentMode: payment,
        paymentDetails: data?.paymentDetails,
        reference: data?.reference,
        additionalNotes: data?.additionalNotes,
      },
      donationTypeId: donationType,
      donationHeadId: donation,
      receiptDate: data?.donationDate,
    };
    try {
      await auth.patchDonationReceipt(
        formData?.id,
        fields,
        handleFailureUpdate,
        handleSuccessUpdate
      );
    } catch (error) {
      console.error("Employee Creation failed:", error);
      handleFailureUpdate();
    }
    fetchReceipts(page, pageSize, searchKeyword);
    handleDialogClose();
  }

  const handlePaymentChange = (newValue) => {
    setPayment(newValue);
  };

  return (
    <>
      <Dialog open={open} onClose={handleDialogClose} maxWidth="md">
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "flex-start", md: "flex-start" },
            fontSize: { xs: 20, md: 26 },
            height: "45px",
          }}
        >
          {getDialogTitle()}
          <Box
            sx={{
              position: "absolute",
              top: "9px",
              right: "10px",
            }}
          >
            <IconButton
              size="small"
              onClick={handleDialogClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#d6d6f5",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent>
          <>
            <Grid
              sx={{
                backgroundColor: "#d6d6f5",
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
                mb: 4,
                mt: -3,
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Donor Information
              </Typography>
            </Grid>
            <Grid container spacing={2}>
              {user?.organisationCategory === "SUPER_ADMIN" && (
                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth error={Boolean(errors.tenantName)}>
                    <Controller
                      name="tenantName"
                      control={control}
                      rules={{
                        required: !isEditMode() ? "NGO Name is required" : false,
                      }}
                      render={({ field }) => (
                        <SelectAutoComplete
                          id="tenantName"
                          label="NGO Name"
                          nameArray={tenantsList}
                          value={tenantId}
                          onChange={(event) => {
                            field.onChange(event.target?.value);
                            setTenantId(event.target?.value);
                          }}
                        />
                      )}
                    />
                    {errors.tenantName && (
                      <FormHelperText sx={{ color: "error.main" }}>
                        {errors.tenantName.message}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Grid>
              )}
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth error={Boolean(errors.tenantName)}>
                  <Controller
                    name="donorName"
                    control={control}
                    rules={{
                      required: !isEditMode() ? "Donor is required" : false,
                    }}
                    render={({ field }) => (
                      <SelectAutoComplete
                        id="donorName"
                        label="Select Donor"
                        nameArray={donorsList}
                        value={donorId}
                        onChange={(event) => {
                          field.onChange(event.target?.value);
                          setDonorId(event.target?.value);
                        }}
                      />
                    )}
                  />
                  {errors.donorName && (
                    <FormHelperText sx={{ color: "error.main" }}>
                      {errors.donorName.message}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid container item xs={12} md={4} spacing={2} sx={{mt:0}}>
                <Grid item>
                  <Typography className="data-field">
                    {" "}
                    Mobile Number :
                  </Typography>
                </Grid>
                <Grid item>
                  <Typography style={{ fontWeight: "bold" }}>
                    {donorData?.contactNumber}
                  </Typography>
                </Grid>
              </Grid>
                <Grid container item xs={12} md={4} spacing={2} sx={{ml:0}}  >
                <Grid item>
                  <Typography className="data-field">
                    {" "}
                   Email :
                  </Typography>
                </Grid>
                <Grid item>
                  <Typography style={{ fontWeight: "bold" }}>
                    {donorData?.email}
                  </Typography>
                </Grid>
              </Grid>

               <Grid container item xs={12} md={4} spacing={2} >
                <Grid item>
                  <Typography className="data-field">
                    {" "}
                   Pan number :
                  </Typography>
                </Grid>
                <Grid item>
                  <Typography style={{ fontWeight: "bold" }}>
                    {donorData?.panNo}
                  </Typography>
                </Grid>
              </Grid>        

                <Grid container item xs={12} md={12} spacing={2} sx={{ml:0}} >
                <Grid item>
                  <Typography className="data-field">
                    {" "}
                   Address :
                  </Typography>
                </Grid>
                <Grid item>
                  <Typography style={{ fontWeight: "bold" }}>
                    {donorData?.donorMetaData?.address}
                  </Typography>
                </Grid>
              </Grid>                    
            </Grid>
          </>

          <>
            <Grid
              sx={{
                backgroundColor: "#d6d6f5",
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
                mb: 5,
                mt: 2,
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Donor Details
              </Typography>
            </Grid>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth error={Boolean(errors.donationType)}>
                  <Controller
                    name="donationType"
                    control={control}
                    rules={{
                      required: getValidationRule("Donation Type", donationType)
                    }}
                    render={({ field }) => (
                      <SelectAutoComplete
                        id="donationType"
                        label="Donation Type"
                        nameArray={donationTypesData}
                        value={donationType}
                        onChange={(event) => {
                          field.onChange(event.target?.value);
                          setDonationType(event.target.value);
                        }}
                      />
                    )}
                  />
                  {errors.donationType && (
                    <FormHelperText sx={{ color: "error.main" }}>
                      {errors.donationType.message}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth error={Boolean(errors.donationHead)}>
                  <Controller
                    name="donationHead"
                    control={control}
                    rules={{
                      required: getValidationRule("Donation Head", donation)
                    }}
                    render={({ field }) => (
                      <SelectAutoComplete
                        id="donationHead"
                        label="Donation Head"
                        nameArray={donationHeadsList}
                        value={donation}
                        onChange={(event) => {
                          field.onChange(event.target?.value);
                          handleDonationChange(event.target.value);
                        }}
                      />
                    )}
                  />
                  {errors.donationHead && (
                    <FormHelperText sx={{ color: "error.main" }}>
                      {errors.donationHead.message}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <Controller
                    name="amount"
                    control={control}
                    rules={{ required: "Amount is required" }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label="Amount"
                        placeholder="Enter your Amount"
                        error={Boolean(errors.amount)}
                        helperText={errors.amount?.message}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <Controller
                    name="donationDate"
                    control={control}
                    rules={{ required: "Donation Date is required" }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        type="date"
                        InputLabelProps={{ shrink: true }}
                        label="Donation Date"
                        placeholder="select Donation Date"
                        error={Boolean(errors.donationDate)}
                        helperText={errors.donationDate?.message}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={4}>
                <FormControl fullWidth error={Boolean(errors.paymentMode)}>
                  <Controller
                    name="paymentType"
                    control={control}
                    rules={{
                      required: getValidationRule("Payment Type", paymentType)
                    }}
                    render={({ field }) => (
                      <SelectAutoComplete
                        id="paymentMode"
                        label="Payment Type"
                        nameArray={paymentTypeData}
                        value={paymentType}
                        onChange={(event) => {
                          field.onChange(event.target?.value);
                          setPaymentType(event.target.value);
                        }}
                      />
                    )}
                  />
                  {errors.paymentMode && (
                    <FormHelperText sx={{ color: "error.main" }}>
                      {errors.paymentMode.message}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>

              {listValues?.find((item) => item.id === paymentType)?.name ===
                "Offline" && (
                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth error={Boolean(errors.paymentMode)}>
                    <Controller
                      name="paymentMode"
                      control={control}
                      rules={{
                        required: (() => {
                          if (!isEditMode()) {
                            return "Payment Mode is required";
                          }
                          return payment ? "" : "Payment is required";
                        })()
                      }}
                      render={({ field }) => (
                        <SelectAutoComplete
                          id="paymentMode"
                          label="Payment Mode"
                          nameArray={paymentModeData}
                          value={payment}
                          onChange={(event) => {
                            field.onChange(event.target?.value);
                            handlePaymentChange(event.target.value);
                          }}
                        />
                      )}
                    />
                    {errors.paymentMode && (
                      <FormHelperText sx={{ color: "error.main" }}>
                        {errors.paymentMode.message}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Grid>
              )}

              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <Controller
                    name="paymentDetails"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        InputLabelProps={{ shrink: true }}
                        label="Payment Details"
                        placeholder="Enter Payment Details"
                        error={Boolean(errors.paymentDetails)}
                        helperText={errors.paymentDetails?.message}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </>
          {listValues?.find((item) => item.id === paymentType)?.name ===
            "Online" && (
            <Grid
              sx={{
                // backgroundColor: "#d6d6f5",
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
                mb: 5,
                mt: 2,
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                *Note : You'll get an send payment link option in actions after
                submitting this receipt
              </Typography>
            </Grid>
          )}

          <>
            <Grid
              sx={{
                backgroundColor: "#d6d6f5",
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
                mb: 5,
                mt: 2,
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Additional Information
              </Typography>
            </Grid>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <Controller
                    name="reference"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label="Reference"
                        placeholder="Enter Reference"
                        error={Boolean(errors.reference)}
                        helperText={errors.reference?.message}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={12}>
                <FormControl fullWidth>
                  <Controller
                    name="additionalNotes"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        multiline
                        rows={3}
                        InputLabelProps={{ shrink: true }}
                        label="Additional Notes "
                        placeholder="Enter Additional Notes "
                        error={Boolean(errors.reference)}
                        helperText={errors.reference?.message}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </>
        </DialogContent>

        <DialogActions
          sx={{
            justifyContent: "flex-end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
            height: "50px",
          }}
        >
          <Button variant="outlined" color="primary" onClick={handleDialogClose}>
            Cancel
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSubmit(isEditMode() ? update : submit)}
          >
            {getButtonText()}
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openDialogContent}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleClose}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

DonationDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  formData: PropTypes.object,
  control: PropTypes.object,
  errors: PropTypes.object,
  setValue: PropTypes.func.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  donationTypesData: PropTypes.array,
  paymentModeData: PropTypes.array,
  paymentTypeData: PropTypes.array,
  tenantsList: PropTypes.array,
  donorsList: PropTypes.array,
  fetchReceipts: PropTypes.func.isRequired,
  page: PropTypes.number,
  pageSize: PropTypes.number,
  searchKeyword: PropTypes.string,
  donationHeadsList: PropTypes.array,
  tenantId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  setTenantId: PropTypes.func,
  donorId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  setDonorId: PropTypes.func,
  donorData: PropTypes.object
};

export default DonationDialog;
