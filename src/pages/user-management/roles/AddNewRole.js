import CloseIcon from "@mui/icons-material/Close";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  TextField
} from "@mui/material";
import axios from "axios";
import PropTypes from "prop-types";
import { useContext, useEffect, useRef, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import DialogData from "./DialogData";

const AddNewRole = ({
  open,
  onClose,
  fetchRoles,
  roles,
  data,
  page,
  pageSize,
  selectRoleId,
  setSelectRoleId,
}) => {
  const { rolesDetails, setRolesDetails,user } = useContext(AuthContext);
 
  const {
    setValue,
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm();

  const [initialSiteMapData, setInitialSiteMapData] = useState([]);
  const [tenantsList, setTenantsList] = useState([]);
  const [tenantId, setTenantId] = useState("");

  // Initialize form and permissions when dialog opens
  useEffect(() => {
    if (data) {
      setSelectRoleId(data?.parentRoleId || "");
      setInitialSiteMapData(data?.permissions || []);
      setValue("name", data?.name || "");
      setValue("description", data?.description || "");
      setTenantId(data?.orgId || "");
    }
  }, [data, setValue]);

  useEffect(() => {
    // Fetch all tenants
    axios({
      method: "get",
      url: getUrl(authConfig.organisationsEndpoint) + "/TENANT",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setTenantsList(
          res.data.map((item) => ({
            value: item.id,
            key: item.name,
          }))
        );
      })
      .catch((err) => console.log("TENANT Dropdown error", err));
  }, []);

  const auth = useAuth();
  const [dialogMessage, setDialogMessage] = useState("");
  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);

  const handleClose = () => {
    onClose();
    setSelectRoleId("");
    setTenantId("");
    setValue("name", "");
    setValue("description", "");
    setRolesDetails({});
    setInitialSiteMapData([]);
    setOpenDialog(false);
    reset();
  };

  const handleCancel = () => {
    const message = `
        <div>
          <h3>Are you sure you want to leave the page? Unsaved changes may be lost</h3>
        </div>
      `;
    setDialogMessage(message);
    setOpenDialog(true);
  };

  const handleCancelClose = () => {
    setOpenDialog(false);
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
    if (!data?.id) {
      handleClose();
    }
  };



  const handleSuccess = () => {
    const message = `
    <div> 
      <h3>${
        !data?.id ? "Role added Successfully." : "Role updated Successfully."
      }</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = (err) => {
    let message;
    if (
      err.response?.data?.message?.toLowerCase().includes("role already exist")
    ) {
      message = `
        <div>
            <h3>A role with this name already exists. Please choose a different name.</h3>
        </div>
        `;
    } else if (
      err.response?.data?.name?.toLowerCase().includes("name is mandatory")
    ) {
      message = `
      <div>
          <h3>Role name cannot be empty or contain only spaces.</h3>
      </div>
      `;
    } else {
      message = `
        <div>
            <h3>${
              !data?.id
                ? "Failed to Add a Role. Please try again later."
                : "Failed to update Role. Please try again later."
            }</h3>
        </div>
        `;
    }
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const isApiCalling = useRef(false);

  async function handleUpdate(data) {
    if (isApiCalling.current) {
      return;
    }

    

    const fields = {
      name: data?.name,
      description: data?.description,
      parentRoleId: selectRoleId,
      orgId: user?.organisationCategory === "SUPER_ADMIN" ? tenantId:user?.orgId,
      permissions: initialSiteMapData,
    };
    setIsSubmitting(true);
    try {
      await auth.rolePatch(
        rolesDetails?.id,
        fields,
        handleFailure,
        handleSuccess
      );
    } catch (error) {
      console.error("Role Update failed:", error);
      handleFailure();
    } finally {
      setIsSubmitting(false);
      isApiCalling.current = false;
      fetchRoles(page, pageSize);
    }
  }

  const [isSubmitting, setIsSubmitting] = useState(false);
  async function handleSave(data) {
    if (isApiCalling.current) {
      return;
    }
    const fields = {
      name: data?.name,
      orgId: user?.organisationCategory === "SUPER_ADMIN" ? tenantId:user?.orgId,
      description: data?.description,
      parentRoleId: selectRoleId,
      permissions: initialSiteMapData,
    };
    setIsSubmitting(true);
    try {
      await auth.rolePost(
        fields,
        handleFailure,
        handleSuccess
      );
    } catch (error) {
      console.error("Role Creation failed:", error);
      handleFailure();
    } finally {
      setIsSubmitting(false);
      isApiCalling.current = false;
      fetchRoles(page, pageSize);
    }
  }

  return (
    <>
      <Dialog open={open} onClose={handleCancel} maxWidth="md" fullScreen>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            height: "50px",
            marginLeft: { xl: 3.8, lg: 4, md: 3.8, sm: 4, xs: 3.4 },
          }}
          textAlign={"center"}
        >
          {!data?.id ? "Add New Role" : "Update Role"}
          <Box
            sx={{
              position: "absolute",
              top: "8px",
              right: "65px",
              marginRight: { xs: 4, sm: 4.5, md: 5, lg: 6, xl: 10 },
            }}
          >
            {!data?.id ? (
              <Button
                variant="contained"
                color="primary"
                disabled={isSubmitting}
                size="small"
                onClick={handleSubmit(handleSave)}
              >
                Save
              </Button>
            ) : (
              <Button
                variant="contained"
                color="primary"
                size="small"
                disabled={isSubmitting}
                onClick={handleSubmit(handleUpdate)}
              >
                Update
              </Button>
            )}
          </Box>
          <Box
            sx={{
              position: "absolute",
              top: "8px",
              right: "10px",
              marginRight: { xl: 5.5, lg: 5.8, md: 5, sm: 5.5, xs: 6 },
            }}
          >
            <IconButton
              size="small"
              onClick={handleCancel}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <CloseIcon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={5} alignItems={"center"}>
           
            {user?.organisationCategory === "SUPER_ADMIN" && (

<Grid item xs={12} sm={4}>
              <FormControl fullWidth error={Boolean(errors.tenantId)}>
                <Controller
                  name="tenantId"
                  control={control}
                  rules={{
                    required: (() => {
                      // Extract nested ternary operation for better readability
                      if (!data?.id) return "NGO Name is required";
                      return tenantId ? "" : "NGO Name is required";
                    })(),
                  }}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="tenantId"
                      label="NGO Name"
                      nameArray={tenantsList}
                      value={tenantId}
                      defaultValue={data?.orgId}
                      onChange={(event) => {
                        field.onChange(event.target?.value);
                        setTenantId(event.target?.value);
                      }}
                    />
                  )}
                />
                {errors.tenantId && (
                  <FormHelperText sx={{ color: "error.main" }}>
                    {errors.tenantId.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
            )}
            

            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <Controller
                  name="name"
                  control={control}
                  rules={{
                    required: "Role Name is required",
                    minLength: {
                      value: 3,
                      message: "Role Name must be at least 3 characters",
                    },
                    validate: (value) =>
                      /[a-zA-Z]/.test(value) ||
                      "Role name must contain at least one letter",
                  }}
                  defaultValue={data?.name || ""}
                  render={({ field, fieldState }) => (
                    <TextField
                      {...field}
                      label="Role Name"
                      placeholder="Enter Role Name"
                      size="small"
                      InputLabelProps={{ shrink: true }}
                      inputProps={{ maxLength: 100 }}
                      error={Boolean(fieldState.error)}
                      helperText={fieldState.error?.message}
                      aria-describedby="role-name"
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <Controller
                  name="description"
                  control={control}
                  rules={{
                    required: "Description is required",
                    minLength: {
                      value: 3,
                      message: "Description must be at least 3 characters",
                    },
                  }}
                  defaultValue={data?.description || ""}
                  render={({ field, fieldState }) => (
                    <TextField
                      {...field}
                      label="Description"
                      placeholder="Enter description"
                      size="small"
                      InputLabelProps={{ shrink: true }}
                      inputProps={{ maxLength: 100 }}
                      error={Boolean(fieldState.error)}
                      helperText={fieldState.error?.message}
                      aria-describedby="description"
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Box
              sx={{
                width: "100%",
                borderRadius: 1,
                textAlign: "center",
                marginTop: 3,
                border: (theme) => `1px solid ${theme.palette.divider}`,
              }}
            ></Box>
            <Grid item xs={12} sm={12}>
              <DialogData
                initialSiteMapData={initialSiteMapData}
                setInitialSiteMapData={setInitialSiteMapData}
              />
            </Grid>
          </Grid>
        </DialogContent>
      </Dialog>
      <Dialog
        open={openDialog}
        onClose={handleCancelClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleClose}
              sx={{ margin: "auto", width: 100 }}
            >
              Yes
            </Button>
            <Button
              variant="contained"
              onClick={handleCancelClose}
              sx={{ margin: "auto", width: 100 }}
            >
              No
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

AddNewRole.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  fetchRoles: PropTypes.func.isRequired,
  roles: PropTypes.array,
  data: PropTypes.array,
  page: PropTypes.number,
  pageSize: PropTypes.number,
  selectRoleId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  setSelectRoleId: PropTypes.func.isRequired
};

export default AddNewRole;
