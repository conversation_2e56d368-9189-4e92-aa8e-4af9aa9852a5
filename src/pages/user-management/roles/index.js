import {
  <PERSON>,
  <PERSON><PERSON>,
  CardContent,
  <PERSON>,
  Divider,
  <PERSON>rid,
  <PERSON>u,
  <PERSON>uItem,
  Typography
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { useRouter } from "next/router";
import { useContext, useEffect, useState } from "react";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import FallbackSpinner from "src/@core/components/spinner";
import authConfig from "src/configs/auth";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useRBAC } from "src/pages/permission/RBACContext";
import AddNewRole from "./AddNewRole";
import AdvancedSearch from "./AdvancedSearch";
import Columns from "./Columns";
import DeActivateDialog from "src/@core/components/custom-components/DeActivateDialog";


const ParentComponent = () => {
  const { roleId, setRoleId, rolesDetails, setRolesDetails } =
    useContext(AuthContext);

  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [currentRow, setCurrentRow] = useState(null);
  const [selectedFilters, setSelectedFilters] = useState([]); // New state for filters
  const [rolesList, setRolesList] = useState([]);
  const [openAdvancedSearch, setOpenAdvancedSearch] = useState(false);
  const [searchingState, setSearchingState] = useState(false);
  const handleAdvancedSearch = () => {
    setOpenAdvancedSearch(!openAdvancedSearch);
  };

  const [initialRowCount, setInitialRowCount] = useState(null);

  const handleApplyFilters = (filters) => {
    setSelectedFilters(filters); // Set filters for chips
    setSearchingState(true); // Trigger search
  };

  // Function to remove a single filter
  const handleRemoveFilter = (filterKey) => {
    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== filterKey)
    );
  };

  // Clear all filters
  const clearAllFilters = () => {
    setSelectedFilters([]);
    setSearchingState(false); // Reset search state if needed
  };

  const fetchRoles = async (currentPage, currentPageSize, selectedFilters) => {
    const url = getUrl(authConfig.rolesEndpoint) + "/all";

    const headers = getAuthorizationHeaders({
      contentType: authConfig.getAllMIMEType,
      accept: authConfig.getAllAcceptMIMEType,
    });

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
    };

    selectedFilters?.forEach((filter) => {
      const key = filter.key;
      data[key] = filter.value;
    });

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (initialRowCount == null) {
        setInitialRowCount(response.data.rowCount || 0);
      }

      if (response.data) {
        setRolesList(response.data?.rolesResponseDTOS || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching roles:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRoles(page, pageSize, selectedFilters);
  }, [page, pageSize, selectedFilters]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);

  const [anchorEl, setAnchorEl] = useState(null);
  const [menuPosition, setMenuPosition] = useState(null);

  // Handle menu click
  const handleMenuClick = (params, event) => {
    setRoleId({
      ...roleId,
      id: params.row.id,
    });
    setCurrentRow(params.row); // Save the row details for later actions
    setAnchorEl(event.currentTarget); // Set anchor to row's position
    setMenuPosition({ mouseX: event.clientX, mouseY: event.clientY }); // Capture mouse click position
  };

  const handleMenuClose = () => {
    setAnchorEl(null); // Close menu
    setMenuPosition(null); // Reset menu position
  };

  const onClickViewProfile = () => {
    setOpen(true);
    handleMenuClose();
  };
  
  const handleClone = async () => {
    if (currentRow) {
      try {
        const response = await axios({
          method: "get",
          url: `${getUrl(authConfig.rolesEndpoint)}/${currentRow?.id}`, // Fetch role details using roleId
          headers: getAuthorizationHeaders(authConfig.getMIMEType),
        });
        const roleData = response.data;
        const clonedData = {
          id: "", // Ensure no roleId for new role creation
          name: "", // Empty for user input
          description: "", // Empty for user input
          parentRoleId: roleData.parentRoleId, // Prepopulate from fetched data
          permissions: roleData.permissions, // Prepopulate from fetched data
        };
        setRolesDetails(clonedData); // Set cloned data in context
        setOpen(true); // Open dialog
      } catch (err) {
        console.log("Error fetching role details for cloning:", err);
      }
    }
    handleMenuClose();
  };

  const onClickToggleStatus = () => {
    setOpenDeleteDialog(true);
    handleMenuClose();
  };

  const [selectRoleId, setSelectRoleId] = useState("");



  const handleOpen = async () => {
    // Set default rolesDetails with tenant parentRoleId
    const tenantMemberRoleId = authConfig.tenantMemberRoleId;
    setRolesDetails({
      id: "",
      name: "",
      description: "",
      parentRoleId: tenantMemberRoleId,
      permissions: [], // Initialize as empty, will be populated after fetch
    });
    setSelectRoleId(tenantMemberRoleId);
  
    // Fetch permissions for tenant role
    try {
      const response = await axios({
        method: "get",
        url: `${getUrl(authConfig.rolesEndpoint)}/${tenantMemberRoleId}`
      });
      const permissions = response.data.permissions || [];
      setRolesDetails((prev) => ({
        ...prev,
        permissions, // Set fetched permissions
      }));
      setOpen(true);
    } catch (err) {
      console.error("Error fetching tenant role permissions:", err);
      setOpen(true); // Open dialog even if fetch fails
    }
  };

  // Function to handle dialog close
  const handleClose = () => {
    setOpen(false);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchRoles(page, pageSize);
  };

  const columns = Columns().concat([
    {
      field: "actions",
      headerName:"Actions",
      width:100,
      sortable:false,
      renderCell: (params) => (
        <>
            <CustomAvatar
              skin="light"
              variant="rounded"
              sx={{
                mr: { xs: 2, lg: 4 },
                width: 34,
                height: 34,
                cursor: "pointer",
              }}
              onClick={(event) => handleMenuClick(params, event)}
            >
              <Icon icon="bi:three-dots-vertical" />
            </CustomAvatar>
            {currentRow && (
              <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
              anchorReference="anchorPosition"
              anchorPosition={
                menuPosition
                  ? { top: menuPosition.mouseY, left: menuPosition.mouseX }
                  : undefined
              }
            >
              {currentRow?.parentRoleId !== null && (
                <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
              )}
              <MenuItem onClick={handleClone}>Clone</MenuItem>
              <MenuItem onClick={onClickToggleStatus}>
                {currentRow?.isActive ? "Deactivate" : "Activate"}
              </MenuItem>
            </Menu>
            )}    
        </>
      )
    }
  ]);

  const { canMenuPageSection, rbacRoles } = useRBAC();

  const router = useRouter();

  const canAccessRoles = (requiredPermission) =>
    canMenuPageSection(MENUS.TOP, PAGES.USER_MANAGEMENT, PAGES.ROLES , requiredPermission);

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessRoles(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  if(canAccessRoles(PERMISSIONS.READ)) {
  return (
    <>
      <div>
        <Box
          sx={{
            py: 3,
            px: 6,
            rowGap: 2,
            columnGap: 4,
            display: "flex",
            flexWrap: "wrap",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
              <Typography variant="h6">Roles</Typography>
            </Grid>

            <Grid item xs={12} sm={8}>
              <Grid
                container
                spacing={2}
                alignItems="center"
                justifyContent="flex-end"
              >
                <Grid
                  item
                  sx={{
                    paddingTop: { xs: "15px", sm: "25px" },
                    mr: "6px",
                    ml: "6px",
                  }}
                >
                  <AdvancedSearch
                    open={openAdvancedSearch}
                    toggle={handleAdvancedSearch}
                    searchingState={searchingState}
                    setSearchingState={setSearchingState}
                    selectedFilters={selectedFilters}
                    clearAllFilters={clearAllFilters}
                    onApplyFilters={handleApplyFilters}
                  />
                </Grid>
                <Grid item xs="auto" sm="auto">
                  <Button variant="contained" onClick={handleOpen}>
                    Add New Role
                  </Button>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Box>
        <Divider />
        <CardContent>
          <Box sx={{ display: "flex", flexWrap: "wrap", mb: 2 }}>
            {selectedFilters.map((filter) => {
              // Find the corresponding object in convertedArray with the matching value
              // const matchedItem = convertedArray.find(
              //   (item) => item.value === filter.value
              // );

              // Display the key of matchedItem if the label is "roleFilter", otherwise display the value
              const displayValue =
                filter.label === "Role" && matchedItem
                  ? matchedItem.key
                  : filter.value;

              return (
                filter.label && ( // Only render the Chip if label is not null or undefined
                  <Chip
                    key={filter.key}
                    label={`${filter.label}: ${displayValue}`}
                    onDelete={() => handleRemoveFilter(filter.key)}
                    sx={{ mr: 1, mb: 1 }}
                  />
                )
              );
            })}
          </Box>

          <div style={{ height: 400, width: "100%" }}>
            {loading ? (
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                height="60vh"
              >
                <FallbackSpinner />
              </Box>
            ) : (
                <DataGrid
                  rows={rolesList}
                  columns={columns}
                  pagination
                  pageSize={pageSize}
                  page={page - 1}
                  rowsPerPageOptions={rowsPerPageOptions}
                  rowCount={rowCount}
                  paginationMode="server"
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  rowHeight={38}
                  headerHeight={38}
                />
            )}
          </div>
        </CardContent>
      </div>

      <AddNewRole
        open={open}
        onClose={handleClose}
        fetchRoles={fetchRoles}
        data={rolesDetails}
        page={page}
        pageSize={pageSize}
        selectRoleId={selectRoleId}
        setSelectRoleId={setSelectRoleId}
      />
      <DeActivateDialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        data={currentRow}
        deleteEndpoint={authConfig.rolesEndpoint}
      />
    </>
  );
}
else {
  return null;
}
};

export default ParentComponent;
