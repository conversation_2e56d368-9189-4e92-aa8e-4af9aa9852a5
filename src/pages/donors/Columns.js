import { <PERSON>u, <PERSON>uI<PERSON>, Tooltip } from "@mui/material";
import PropTypes from "prop-types";
import { useRouter } from "next/router";
import { useContext } from "react";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import CustomChip from "src/@core/components/mui/chip";
import { AuthContext } from "src/context/AuthContext";

const userStatusObj = {
  true: "Active",
  false: "Inactive",
};

const mapIsActiveToLabel = (isActive) => {
  return userStatusObj[isActive] || "Unknown";
};

const useColumns = ({
  menu,
  setMenu,
  currentRow,
  setCurrentRow,
  setOpenDialog,
  setOpenDeleteDialog,
  tenantsList,
  setSendWhatsAppDialog,
  setSendEmailDialog,
  setSendSMSDialog,
}) => {
  const { donorId,setDonorId,setDonorDetails,user } = useContext(AuthContext);
  const router = useRouter();

  return [
    {
      field: "name",
      headerName: "Name",
      flex: 0.15,
      minWidth: 140,
      renderCell: (params) => (
        <button
          type="button"
          style={{
            color: "#1976d2",
            textDecoration: "none",
            cursor: "pointer",
            fontSize: 15,
            fontWeight: 500,
            border: "none",
            background: "transparent",
            padding: 0,
            font: "inherit"
          }}
          onClick={() => router.push(`/donors/${params.row.id}`)}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              e.preventDefault();
              router.push(`/donors/${params.row.id}`)
            }
          }}
          title="View donor details"
        >
          {params?.value}
        </button>
      )
    },
    {
      field: "email",
      headerName: "Email",
      flex: 0.18,
      minWidth: 170,
      renderCell: (params) => (
        <a
          href={params?.value ? `mailto:${params.value}` : "#"}
          style={{
            color: "#1976d2",
            textDecoration: "none",
            cursor: params?.value ? "pointer" : "default",
            fontSize: 15
          }}
          title={params?.value ? "Send email" : "No email available"}
          onClick={(e) => {
            if (!params?.value) {
              e.preventDefault();
            }
          }}
        >
          {params?.value || "N/A"}
        </a>
      )
    },
    {
      field: "contactNumber",
      headerName: "Mobile Number",
      flex: 0.15,
      minWidth: 140,
      renderCell: (params) => (
        <button
          type="button"
          style={{
            color: "#1976d2",
            textDecoration: "none",
            cursor: "pointer",
            fontSize: 15,
            border: "none",
            background: "transparent",
            padding: 0,
            font: "inherit"
          }}
          onClick={() => navigator.clipboard.writeText(params.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              e.preventDefault();
              navigator.clipboard.writeText(params.value);
            }
          }}
          title="Click to copy"
        >
          {params?.value || "N/A"}
        </button>
      )
    },
    {
      field: "panNo",
      headerName: "Pan No",
      flex: 0.13,
      minWidth: 120,
    },
    user?.organisationCategory === "SUPER_ADMIN"
      ? {
          field: "tenantOrgId",
          headerName: "NGO Name",
          flex: 0.14,
          minWidth: 140,
          renderCell: (params) => {
        const org = tenantsList?.find(
          (item) => item?.value === params?.row?.tenantOrgId
        );
        return (
          <Tooltip title={org?.key || "No NGO assigned"}>
            <span>{org?.key || "N/A"}</span>
          </Tooltip>
        );
      },
        }
      : null,
    {
      field: "isActive",
      headerName: "Status",
      flex: 0.10,
      minWidth: 100,
      renderCell: ({ row }) => (
        <CustomChip
          rounded
          skin="light"
          size="small"
          label={mapIsActiveToLabel(row?.isActive)}
          color={row?.isActive ? "success" : "error"}
          sx={{ textTransform: "capitalize" }}
        />
      ),
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 0.08,
      minWidth: 80,
      sortable: false,
      disableClickEventBubbling: true,
      renderCell: (params) => {

          const handleClickMenu = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          setCurrentRow(params.row);
          setDonorId({
            ...donorId,
            id: params.row.id,
          });
        };

        const onClickViewProfile = () => {
          setOpenDialog(true);
          setMenu(null);
        };

        const onClickToggleStatus = () => {
          setOpenDeleteDialog(true);
          setMenu(null);
        };

        const onClickSendWhatsApp = () => {
          setSendWhatsAppDialog();
          setMenu(null);
        };

        const onClickSendEmail = () => {
          setSendEmailDialog();
          setMenu(null);
        };

        const onClickSendSMS = () => {
          setSendSMSDialog();
          setMenu(null);
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CustomAvatar
              skin="light"
              variant="rounded"
              sx={{
                mr: { xs: 2, lg: 4 },
                width: 34,
                height: 34,
                cursor: "pointer",
              }}
              onClick={handleClickMenu}
            >
              <Icon icon="bi:three-dots-vertical" />
            </CustomAvatar>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              open={Boolean(menu)}
               onClose={() => {
                setDonorDetails(null);
                setMenu(null);
              }}
            >

               <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
               <MenuItem onClick={onClickSendWhatsApp}>
                 Send message via WhatsApp
               </MenuItem>
               <MenuItem onClick={onClickSendEmail}>
                 Send message via Email
               </MenuItem>
               <MenuItem onClick={onClickSendSMS}>
                 Send Text Message
               </MenuItem>
               <MenuItem onClick={onClickToggleStatus}>
                  {currentRow?.isActive ? "Deactivate" : "Activate"}
                </MenuItem>
            </Menu>
          </div>
        );
      },
    },
  ].filter(Boolean);
};

useColumns.propTypes = {
  menu: PropTypes.object,
  setMenu: PropTypes.func.isRequired,
  currentRow: PropTypes.object,
  setCurrentRow: PropTypes.func.isRequired,
  setOpenDialog: PropTypes.func.isRequired,
  setOpenDeleteDialog: PropTypes.func.isRequired,
  tenantsList: PropTypes.array,
  setSendWhatsAppDialog: PropTypes.func.isRequired,
  setSendEmailDialog: PropTypes.func.isRequired,
  setSendSMSDialog: PropTypes.func.isRequired
};

export default useColumns;
