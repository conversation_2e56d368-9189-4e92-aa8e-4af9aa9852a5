import {
  Box,
  CardContent,
  FormHelperText
} from "@mui/material";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import IconButton from "@mui/material/IconButton";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import Icon from "src/@core/components/icon";
import FallbackSpinner from "src/@core/components/spinner";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import useColumns from "../donation-head/Columns";
import DeleteDialog from "../donation-receipts/DeleteDialog";


const userStatusObj = {
  true: "Active",
  false: "InActive",
};

const DonationHead = () => {
  const [userList, setUserList] = useState([]);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [openActivateDialog, setOpenActivateDialog] = useState(false);

  const [openDialog, setOpenDialog] = useState(false);

  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const auth = useAuth();
  const formData =[];

  const {
    locationsData,
    setLocationsData,
    locationsDataDetails,
    listValues,
    getAllListValuesByListNameId,
  } = useContext(AuthContext);

  const [searchKeyword, setSearchKeyword] = useState("");
  const [expanded, setExpanded] = useState(true);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [currentRow, setCurrentRow] = useState();
  const [rowCount, setRowCount] = useState(0);
  const [dialogMessage, setDialogMessage] = useState("");
  const handleClose = () => setOpenDialogContent(false);

  const [keyword, setKeyword] = useState("");

  const [loading, setLoading] = useState(true);

  const [selectedZoneId, setSelectedZoneId] = useState("");
  const [zonesData, setZonesData] = useState(null);

  const {
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: "",
    },
  });

  const handleZoneSuccess = (data) => {
    setZonesData(data?.listValues);
  };

  const handleError = (error) => {
    console.error("Employees page:", error);
  };

  useEffect(() => {
    if (authConfig) {
      getAllListValuesByListNameId(
        authConfig.allZonesListNameId,
        handleZoneSuccess,
        handleError
      );
    }
  }, [authConfig]);

  async function submit(data) {
    const fields = {
      name: data?.location?.trim(),
      listNamesId: authConfig.locationlistNameId,
    };

    try {
      const response = await auth.postService(fields);

      if (response) {
        const { id } = response;

        const zoneFields = {
          locationId: id,
          zoneId: selectedZoneId,
        };
        const res = await auth.postLocationZoneMap(zoneFields);

        if (res) {
          handleSuccess();
        }
      } else {
        handleFailure();
      }
    } catch (error) {
      console.error("Service Data Creation failed:", error);
      handleFailure();
    }

    setOpenCreateDialog(false);
    setSelectedZoneId("");
    reset();
    fetchUsers(page, pageSize, searchKeyword);
  }

  const handleSuccess = () => {
    const message = `
      <div> 
        <h3> Location added Successfully.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = (err) => {
    let message;
    if (err?.response?.status == 400) {
      message = `
      <div>
        <h3>Location already exists!</h3>
      </div>
    `;
    } else {
      message = `
      <div> 
        <h3> Failed to Add Location. Please try again later.</h3>
      </div>
    `;
    }

    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    // const url = getUrl(authConfig.locationZonesGetAll);

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        const locations = response.data.locationZonesResponse || [];

        setUserList(locations);

        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCloseCreateDialog = () => {
    reset();
    setOpenCreateDialog(false);
    setOpenDialog(false);
    setSelectedZoneId("");
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchUsers(page, pageSize, searchKeyword);
  };

  const handleCloseActivateDialog = () => {
    setOpenActivateDialog(false);
    fetchUsers(page, pageSize, searchKeyword);
  };

  useEffect(() => {
    fetchUsers(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };

  const handleOpenDialog = () => {
    setOpenCreateDialog(true);
  };

  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  const columns = useColumns({
    menu,
    setMenu,
    currentRow,
    setCurrentRow,
    setLocationsData,
    locationsData,
    setOpenDialog,
    setOpenDeleteDialog,
    setOpenActivateDialog,
    handleCloseMenuItems,
  });
  

  return (
    <>
      <Grid>

          
          <CardContent>
            <div style={{ height: 430, width: "100%" }}>
              {loading ? (
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  height="60vh"
                >
                  <FallbackSpinner />
                </Box>
              ) : (
                <DataGrid
                  rows={userList}
                  columns={columns}
                  pagination
                  pageSize={pageSize}
                  page={page - 1}
                  rowsPerPageOptions={rowsPerPageOptions}
                  rowCount={rowCount}
                  paginationMode="server"
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  rowHeight={38}
                  headerHeight={38}
                />
              )}
            </div>
          </CardContent>
          <DeleteDialog
            open={openDeleteDialog}
            onClose={handleCloseDeleteDialog}
            data={currentRow}
          />
          {/* <ActivateDialog
            open={openActivateDialog}
            onClose={handleCloseActivateDialog}
            data={currentRow}
          /> */}
       
      </Grid>
    
      <Dialog
        open={openCreateDialog || openDialog}
        onClose={handleCloseCreateDialog}
        fullWidth
        // maxWidth="sm"
        scroll="paper"
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "flex-start", md: "flex-start" },
            fontSize: { xs: 20, md: 26 },
            height:"50px",
            marginLeft:{xl:3.5,lg:3.5,md:3.5,m:3.5,xs:3.5},
          }}
          textAlign={"center"}
          //fontWeight={"bold"}
        >
            {!formData || Object.keys(formData).length === 0
            ? "Add Donation Head"
            : "Update Donation Head"}
          <Box sx={{ position: "absolute", top: "9px", right: "10px",marginRight:{xl:5,lg:5,md:5,m:5,xs:5} }}>
            <IconButton
              size="small"
              onClick={handleCloseCreateDialog}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <Grid container spacing={5}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="educationFund"
                  control={control}
                  rules={{ required: "Education Fund is required" }}
                  render={({ field }) => (
                    <NameTextField
                      {...field}
                      size="small"
                      label="Education Fund"
                      InputLabelProps={{ shrink: true }}
                      placeholder="Enter your Education Fund"
                      error={Boolean(errors.location)}
                      helperText={errors.location?.educationFund}
                      aria-describedby="Section1-educationFund"
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={Boolean(errors.tenantName)}>
                  <Controller
                    name="tenantName"
                    control={control}
                    rules={{ required: "NGO Name is required" }}
                    render={({ field }) => (
                      <SelectAutoComplete
                        id="tenantName"
                        label="NGO Name"
                        nameArray={[]}
                        // value={locate}
                        onChange={(event) => {
                          field.onChange(event.target?.value);
                          handleLocationChange(event.target.value);
                        }}
                      />
                    )}
                  />
                  {errors.donationType && (
                    <FormHelperText sx={{ color: "error.main" }}>
                      {errors.donationType.message}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "flex-end", // Aligns the buttons to the right
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
            height:"50px",
            marginRight:{xl:5,lg:5,md:5,m:5,xs:5}
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={() => {
              handleCloseCreateDialog();
            }}
          >
            Cancel
          </Button>

          <Button
            display="flex"
            justifyContent="center"
            variant="contained"
            color="primary"
            onClick={handleSubmit(submit)}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default DonationHead;