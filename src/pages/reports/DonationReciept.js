import {
  Box,
  CardContent
} from "@mui/material";
import Grid from "@mui/material/Grid";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import FallbackSpinner from "src/@core/components/spinner";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import useColumns from "../donation-receipts/Columns";
import DeleteDialog from "../donation-receipts/DeleteDialog";
import DonationDialog from "../donation-receipts/DonationDialog";
import ViewDialog from "../donation-receipts/ViewDialog";

const userStatusObj = {
  true: "Active",
  false: "InActive",
};

const LandingPage = () => {
  const [userList, setUserList] = useState([]);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [openActivateDialog, setOpenActivateDialog] = useState(false);

  const [openDialog, setOpenDialog] = useState(false);
  const [openViewDialog, setOpenViewDialog] = useState(false);

  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const auth = useAuth();
  const formData = [];

  const {
    locationsData,
    setLocationsData,
    locationsDataDetails,
    listValues,
    getAllListValuesByListNameId,
  } = useContext(AuthContext);

  const [searchKeyword, setSearchKeyword] = useState("");
  const [expanded, setExpanded] = useState(true);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [currentRow, setCurrentRow] = useState();
  const [rowCount, setRowCount] = useState(0);
  const [dialogMessage, setDialogMessage] = useState("");
  const handleClose = () => setOpenDialogContent(false);

  const [keyword, setKeyword] = useState("");

  const [loading, setLoading] = useState(true);

  const [selectedZoneId, setSelectedZoneId] = useState("");
  const [zonesData, setZonesData] = useState(null);

  const {
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: "",
    },
  });

  const handleZoneSuccess = (data) => {
    setZonesData(data?.listValues);
  };

  const handleError = (error) => {
    console.error("Employees page:", error);
  };

  useEffect(() => {
    if (authConfig) {
      getAllListValuesByListNameId(
        authConfig.allZonesListNameId,
        handleZoneSuccess,
        handleError
      );
    }
  }, [authConfig]);

  async function submit(data) {
    const fields = {
      name: data?.location?.trim(),
      listNamesId: authConfig.locationlistNameId,
    };

    try {
      const response = await auth.postService(fields);

      if (response) {
        const { id } = response;

        const zoneFields = {
          locationId: id,
          zoneId: selectedZoneId,
        };
        const res = await auth.postLocationZoneMap(zoneFields);

        if (res) {
          handleSuccess();
        }
      } else {
        handleFailure();
      }
    } catch (error) {
      console.error("Service Data Creation failed:", error);
      handleFailure();
    }

    setOpenCreateDialog(false);
    setSelectedZoneId("");
    reset();
    fetchUsers(page, pageSize, searchKeyword);
  }

  const handleSuccess = () => {
    const message = `
      <div> 
        <h3> Location added Successfully.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = (err) => {
    let message;
    if (err?.response?.status == 400) {
      message = `
      <div>
        <h3>Location already exists!</h3>
      </div>
    `;
    } else {
      message = `
      <div> 
        <h3> Failed to Add Location. Please try again later.</h3>
      </div>
    `;
    }

    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    const url = getUrl(authConfig.locationZonesGetAll);

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        const locations = response.data.locationZonesResponse || [];

        setUserList(locations);

        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCloseCreateDialog = () => {
    reset();
    setOpenCreateDialog(false);
    setOpenDialog(false);
    setSelectedZoneId("");
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchUsers(page, pageSize, searchKeyword);
  };

  const handleCloseActivateDialog = () => {
    setOpenActivateDialog(false);
    fetchUsers(page, pageSize, searchKeyword);
  };

  useEffect(() => {
    fetchUsers(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };

  const handleOpenDialog = () => {
    setOpenCreateDialog(true);
  };

  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  const [openAdvancedSearch, setOpenAdvancedSearch] = useState(false);
  const [searchingState, setSearchingState] = useState(false);
  const handleAdvancedSearch = () => {
    setOpenAdvancedSearch(!openAdvancedSearch);
  };
  const convertedArray = userList?.map((item) => ({
    value: item.id,
    key: item.name,
  }));
  const clearAllFilters = () => {
    setSelectedFilters([]);
    setSearchingState(false); // Reset search state if needed
  };

  const handleApplyFilters = (filters) => {
    setSelectedFilters(filters); // Set filters for chips
    setSearchingState(true); // Trigger search
  };
  // Function to remove a single filter
  const handleRemoveFilter = (filterKey) => {
    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== filterKey)
    );
  };
  const [selectedFilters, setSelectedFilters] = useState([]);

  const columns = useColumns({
      menu,
      setMenu,
      setOpenDialog,
      setOpenViewDialog,
      setOpenDeleteDialog,
      setOpenActivateDialog,
      setCurrentRow,
      currentRow,
    });
  const rows = [
    {
      id: 1,
      name: "John Doe",
      mobileNumber: "**********",
      type: "Donor",
      donationHead: "Education Fund",
      amount: 5000,
      recipientDate: "2025-04-01",
      isActive: true,
    },
    {
      id: 2,
      name: "Jane Smith",
      mobileNumber: "**********",
      type: "Sponsor",
      donationHead: "Health Campaign",
      amount: 10000,
      recipientDate: "2025-03-20",
      isActive: false,
    },
    {
      id: 3,
      name: "Michael Lee",
      mobileNumber: "**********",
      type: "Donor",
      donationHead: "Relief Fund",
      amount: 7500,
      recipientDate: "2025-02-15",
      isActive: true,
    },
    {
      id: 4,
      name: "Sara Khan",
      mobileNumber: "**********",
      type: "Volunteer",
      donationHead: "Awareness Drive",
      amount: 0,
      recipientDate: "2025-04-05",
      isActive: false,
    },
  ];
  

  return (
    <>
      <Grid>

        <CardContent>
          <div style={{ height: 430, width: "100%" }}>
            {loading ? (
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                height="60vh"
              >
                <FallbackSpinner />
              </Box>
            ) : (
              <DataGrid
                rows={rows}
                columns={columns}
                pagination
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                rowHeight={38}
                headerHeight={38}
              />
            )}
          </div>
        </CardContent>
        <DeleteDialog
          open={openDeleteDialog}
          onClose={handleCloseDeleteDialog}
          data={currentRow}
        />
        {/* <ActivateDialog
          open={openActivateDialog}
          onClose={handleCloseActivateDialog}
          data={currentRow}
        /> */}

        <DonationDialog
          open={openCreateDialog || openDialog}
          onClose={handleCloseCreateDialog}
          onSubmit={submit}
          formData={formData}
          control={control}
          errors={errors}
          handleSubmit={handleSubmit}
        />
        <ViewDialog
          open={openViewDialog}
          onClose={() => setOpenViewDialog(false)}
        />
      </Grid>
    </>
  );
};

export default LandingPage;
