import { useRouter } from "next/router";
import { useEffect } from "react";
import NavTabEmployees from "src/@core/components/custom-components/NavTabEmployees";
import authConfig from "src/configs/auth";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRBAC } from "src/pages/permission/RBACContext";
import SystemConstantsManager from "./SystemConstantsManager";
const SystemConstants = () => {
  const { canMenuPageSection, rbacRoles } = useRBAC();

  const router = useRouter();

  const canAccessSystemConstants = (requiredPermission) =>
    canMenuPageSection(
      MENUS.TOP,
      PAGES.STATIC_DATA,
      PAGES.SYSTEM_CONSTANTS,
      requiredPermission
    );

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessSystemConstants(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  if (canAccessSystemConstants(PERMISSIONS.READ)) {
    return (
      <div>
        <NavTabEmployees
          tabContent1={
            <SystemConstantsManager
              title="State"
              listNameId={authConfig.stateListNameId}
            />
          }
          tabContent2={
            <SystemConstantsManager
              title="Donation Type"
              listNameId={authConfig.donationListNameId}
            />
          }
          tabContent3={
            <SystemConstantsManager
              title="Payment Mode"
              listNameId={authConfig.paymentModeListNameId}
            />
          }
        />
      </div>
    );
  } else {
    return null;
  }
};

export default SystemConstants;
