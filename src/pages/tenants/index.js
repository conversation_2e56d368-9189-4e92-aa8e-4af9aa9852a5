import { yupResolver } from "@hookform/resolvers/yup";
import SearchIcon from "@mui/icons-material/Search";
import {
  Box,
  CardContent,
  Chip,
  Divider,
  InputAdornment,
  Link,
  Menu,
  MenuItem,
  Tooltip
} from "@mui/material";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import { DataGrid } from "@mui/x-data-grid";
import PropTypes from "prop-types";
import axios from "axios";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import CustomChip from "src/@core/components/mui/chip";
import FallbackSpinner from "src/@core/components/spinner";
import authConfig from "src/configs/auth";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useRBAC } from "../permission/RBACContext";
import LongFormDialog from "../profile/sections-longform/LongFormDialog";
import AdvancedSearch from "./AdvancedSearch";
import TenantAddDialog from "./TenantAddDialog";

const userStatusObj = {
  true: "Active",
  false: "Inactive",
  null: "Inactive",
};

const userStatusObj2 = {
  VERIFIED: "Verified",
  PENDING: "Pending",
};

const NoRowsOverlay = ({ userList }) => (
  <Typography
    variant="body1"
    align="center"
    sx={{ marginTop: "120px" }}
  >
    {userList?.length === 0 ? "No Data" : "No Rows"}
  </Typography>
);

NoRowsOverlay.propTypes = {
  userList: PropTypes.array.isRequired,
};

// Create a wrapper component factory to avoid inline component definition
const createNoRowsOverlay = (userList) => () => <NoRowsOverlay userList={userList} />;

const TenantsDetails = () => {
  const [userList, setUserList] = useState([]);

  const [allLoading, setAllLoading] = useState(true);

  const [openDialog, setOpenDialog] = useState(false);

  const [keyword, setKeyword] = useState("");
  const [searchKeyword, setSearchKeyword] = useState("");

  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [currentRow, setCurrentRow] = useState();
  const [rowCount, setRowCount] = useState(0);
  const [openAdvancedSearch, setOpenAdvancedSearch] = useState(false);
  const [searchingState, setSearchingState] = useState(false);

  const [selectedFilters, setSelectedFilters] = useState([]);
  const handleAdvancedSearch = () => {
    setOpenAdvancedSearch(!openAdvancedSearch);
  };

  const clearAllFilters = () => {
    setSelectedFilters([]);
    setSearchingState(false); // Reset search state if needed
  };

  const handleApplyFilters = (filters) => {
    setSelectedFilters(filters); // Set filters for chips
    setSearchingState(true); // Trigger search
  };
  // Function to remove a single filter
  const handleRemoveFilter = (filterKey) => {
    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== filterKey)
    );
  };

  const {
    canMenuPage,
    canMenuPageSection,
    canMenuPageSectionField,
    rbacRoles,
  } = useRBAC();
  const router = useRouter();

  const canAccessTenants = (requiredPermission) =>
    canMenuPage(MENUS.LEFT, PAGES.TENANTS, requiredPermission);
  const canAccessMenu = (requiredPermission, field) =>
    canMenuPageSectionField(
      MENUS.LEFT,
      PAGES.TENANTS,
      "Tenants_DataGrid_Table",
      field,
      requiredPermission
    );

  const canAccessActions = (requiredPermission, section) =>
    canMenuPageSection(MENUS.LEFT, PAGES.TENANTS, section, requiredPermission);

  const {
    control,
    reset,
  } = useForm({
    resolver: yupResolver(),
    mode: "onChange",
  });

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    reset();
    setOpenDialog(false);
  };

  const fetchTenants = async (
    currentPage,
    currentPageSize,
    searchKeyword,
    selectedFilters
  ) => {
    const url = getUrl(authConfig.organisationsEndpoint) + "/admin/all";

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    selectedFilters?.forEach((filter) => {
      const key = filter.key;
      data[key] = filter.value;
    });

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserList(response.data?.tenantsDTOList || []);
        setRowCount(response.data?.count || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setAllLoading(false);
    }
  };

  useEffect(() => {
    fetchTenants(page, pageSize, searchKeyword, selectedFilters);
  }, [page, pageSize, searchKeyword, selectedFilters]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };
  const [editDialog, setEditDialog] = useState(false);

  const handleEditClose = () => {
    setEditDialog(false);
  };
  const mapIsActiveToLabel = (status) => {
    return userStatusObj[status];
  };

  const mapIsActiveToLabel2 = (status) => {
    return userStatusObj2[status];
  };

  const columns = [
    {
      field: "trustName",
      headerName: "Trust Name",
      flex: 2.5,
      minWidth: 185,
    },
    {
      field: "tenantAdminName",
      headerName: "Contact Person",
      flex: 1.9,
      minWidth: 160,
    },
    {
      field: "tenantAdminEmail",
      minWidth: 180,
      headerName: "Contact Person Email",
      flex: 3,
      renderCell: (params) => {
        const email = params?.value;

        return email?.length > 21 ? (
          <Tooltip title={email}>
            <Link
              href={`mailto:${email}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{ color: "#6666ff" }}
            >
              <span>{email}</span>
            </Link>
          </Tooltip>
        ) : (
          <Link
            href={`mailto:${email}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {email}
          </Link>
        );
      },
    },
    {
      field: "verificationStatus",
      headerName: "Verification Status",
      flex: 0.13,
      minWidth: 115,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel2(row.verificationStatus)}
            color={
              row.verificationStatus === "VERIFIED" ? "success" : "warning"
            }
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      field: "tenantAdminMobileNumber",
      minWidth: 135,
      headerName: "Contact Number",
      flex: 0.1,
      renderCell: (params) => {
        const mobileNumber = params?.value;

        return mobileNumber?.length > 9 ? (
          <Tooltip title={mobileNumber}>
            <Link
              href={`mailto:${mobileNumber}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{ color: "#6666ff" }}
            >
              <span>{mobileNumber}</span>
            </Link>
          </Tooltip>
        ) : (
          <Link
            href={`mailto:${mobileNumber}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {mobileNumber}
          </Link>
        );
      },
    },
    {
      field: "isActiveOrg",
      headerName: "Status",
      flex: 0.13,
      minWidth: 115,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.isActiveOrg)}
            color={row.isActiveOrg ? "success" : "error"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 100,
      sortable: false,
      renderCell: (params) => {
        const handleClickMenu = (event) => {
          event.stopPropagation();
          const row = params.row;
          setCurrentRow(row);
          if(row?.verificationStatus === "VERIFIED"){
            setMenu(event.currentTarget);
          }
        };

        const onClickViewProfile = () => {
          setEditDialog(true);
          handleCloseMenuItems();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CustomAvatar
              skin="light"
              variant="rounded"
              sx={{
                mr: { xs: 2, lg: 4 },
                width: 34,
                height: 34,
                cursor: "pointer",
              }}
              onClick={handleClickMenu}
            >
              <Icon icon="bi:three-dots-vertical" />
            </CustomAvatar>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              {(canAccessMenu(PERMISSIONS.FULL_ACCESS, "Edit") && currentRow?.verificationStatus === "VERIFIED") && (
                <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
              )}
              {(canAccessMenu(
                PERMISSIONS.FULL_ACCESS,
                "Activate_Or_Deactivate")
                && currentRow?.verificationStatus === "VERIFIED"
              ) && (
                <MenuItem>
                  {currentRow?.isActive ? "Deactivate" : "Activate"}
                </MenuItem>
              )}
            </Menu>
          </div>
        );
      },
    },
  ];

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessTenants(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);
  if (canAccessTenants(PERMISSIONS.READ)) {
    return (
        <Grid>
          <>
            <Box
              sx={{
                py: 3,
                px: 6,
                rowGap: 2,
                columnGap: 4,
                display: "flex",
                flexWrap: "wrap",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Grid container spacing={3} alignItems="center">
                <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                  <Typography variant="h6" fontWeight={"600"}>
                    Tenants List
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8}>
                  <Grid
                    container
                    spacing={2}
                    alignItems="center"
                    justifyContent="flex-end"
                  >
                    {canAccessActions(
                      PERMISSIONS.FULL_ACCESS,
                      "Main_Search_Field"
                    ) && (
                      <Grid item xs={12} sm="auto">
                        <FormControl>
                          <Controller
                            name="mainSearch"
                            control={control}
                            render={({ field: { onChange } }) => (
                              <TextField
                                id="mainSearch"
                                placeholder="Search by NGO name "
                                value={keyword}
                                onChange={(e) => {
                                  setKeyword(e.target.value);
                                  setSearchKeyword(e.target.value);
                                }}
                                onKeyDown={(e) => {
                                  if (e.key === "Enter") {
                                    setSearchKeyword(keyword);
                                    fetchTenants(
                                      page,
                                      pageSize,
                                      searchKeyword,
                                      selectedFilters
                                    );
                                  }
                                }}
                                sx={{
                                  "& .MuiInputBase-root": {
                                    height: "40px",
                                  },
                                }}
                                InputProps={{
                                  endAdornment: (
                                    <InputAdornment position="start">
                                      <SearchIcon
                                        sx={{
                                          cursor: "pointer",
                                          marginRight: "-15px",
                                        }}
                                        onClick={() => {
                                          setSearchKeyword(keyword);
                                          fetchTenants(
                                            page,
                                            pageSize,
                                            searchKeyword,
                                            selectedFilters
                                          );
                                        }}
                                      />{" "}
                                    </InputAdornment>
                                  ),
                                }}
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                    )}
                    {canAccessActions(
                      PERMISSIONS.FULL_ACCESS,
                      "Advanced_Search"
                    ) && (
                      <Grid item xs="auto" sm="auto" md="auto" lg="auto">
                        <AdvancedSearch
                          open={openAdvancedSearch}
                          toggle={handleAdvancedSearch}
                          searchingState={searchingState}
                          setSearchingState={setSearchingState}
                          selectedFilters={selectedFilters}
                          clearAllFilters={clearAllFilters}
                          onApplyFilters={handleApplyFilters}
                        />
                      </Grid>
                    )}
                    {canAccessActions(PERMISSIONS.FULL_ACCESS, "Add") && (
                      <Grid item xs="auto" sm="auto">
                        <Button variant="contained" onClick={handleOpenDialog}>
                          Add New NGO&nbsp;
                        </Button>
                      </Grid>
                    )}
                  </Grid>
                </Grid>
              </Grid>
            </Box>

            <TenantAddDialog
              open={openDialog}
              onClose={handleCloseDialog}
              fetchTenants={fetchTenants}
              page={page}
              pageSize={pageSize}
              searchKeyword={searchKeyword}
            />
            <Divider />

            <CardContent>          
                <Box sx={{ display: "flex", flexWrap: "wrap", mb: 2 }}>
                  {selectedFilters.map((filter) => {
                    const displayValue =
                      filter.label === "Role" && matchedItem
                        ? matchedItem.key
                        : filter.value;

                    return (
                      filter.label && ( // Only render the Chip if label is not null or undefined
                        <Chip
                          key={filter.key}
                          label={`${filter.label}: ${displayValue}`}
                          onDelete={() => handleRemoveFilter(filter.key)}
                          sx={{ mr: 1, mb: 1 }}
                        />
                      )
                    );
                  })}
                </Box>
              <div style={{ height: 380, width: "100%" }}>
                {allLoading ? (
                  <Box
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                    height="60vh"
                  >
                    <FallbackSpinner />
                  </Box>
                ) : (
                    <DataGrid
                      rows={userList || []}
                      columns={columns}
                      pagination
                      pageSize={pageSize}
                      page={page - 1}
                      rowsPerPageOptions={rowsPerPageOptions}
                      rowCount={rowCount}
                      paginationMode="server"
                      onPageChange={handlePageChange}
                      onPageSizeChange={handlePageSizeChange}
                      rowHeight={38}
                      headerHeight={38}
                      components={{
                        NoRowsOverlay: createNoRowsOverlay(userList),
                      }}
                      getRowId={(row) => row.tenantAdminEmail}
                    />
                )}
              </div>
            </CardContent>
            <LongFormDialog
              open={editDialog}
              handleClose={handleEditClose}
              fetchTenants={fetchTenants}
              page={page}
              pageSize={pageSize}
              currentRow={currentRow}
            />
          </>
        </Grid>
    );
  } else {
    return null;
  }
};

export default TenantsDetails;
