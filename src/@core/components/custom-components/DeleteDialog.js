import axios from "axios";
import PropTypes from "prop-types";
import { useState } from "react";
import authConfig from "src/configs/auth";

import {
  Box,
  Button,
  CircularProgress,
  DialogContentText
} from "@mui/material";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";

import { getAuthorizationHeaders } from "src/helpers/utils";

const DeleteDialog = ({ open, onClose, data, deleteEndpoint, name }) => {
  const [loading, setLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  async function onDelete(data) {
    setLoading(true);

    const url = authConfig.baseURL + deleteEndpoint + "/" + data.id;
    const headers = getAuthorizationHeaders();
    
    try {
        if (data?.isActive) {
            // Call axios.delete if isActive is true
            await axios.delete(url, { headers });
            setSuccessMessage(`${name} Deactivated successfully.`);
          } else {
            // Call axios.patch if isActive is false
            await axios.patch(url, { headers });
            setSuccessMessage(`${name} Activated successfully.`);
          }
      
    } catch (error) {
      console.error("Delete operation failed", error);
    } finally {
      setLoading(false);
      onClose();
    }
  }

  const handleCloseSnackbar = () => {
    setSuccessMessage("");
  };

  const buttonLabel = data?.isActive ? "Deactivate" : "Activate";

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
                 {data?.isActive ? `Are you sure you want to deactivate ${name} ?`:`Are you sure, you want to activate the ${name} ?`}
             
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={() => onDelete(data)}
              sx={{ margin: "auto", width: 100 }}
              disabled={loading}
            >
              {loading ? (
                <CircularProgress size={24} />
              ) : (
                buttonLabel
              )}
            </Button>
            <Button
              variant="contained"
              onClick={onClose}
              sx={{ margin: "auto", width: 100 }}
              disabled={loading}
            >
              Cancel
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
      <Dialog
        open={!!successMessage}
        onClose={handleCloseSnackbar}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              {successMessage}
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleCloseSnackbar}
              sx={{ margin: "auto", width: 100 }}
              autoFocus
            >
              OK
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

DeleteDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  data: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    isActive: PropTypes.bool,
    name: PropTypes.string,
  }).isRequired,
  deleteEndpoint: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired
};

export default DeleteDialog;
