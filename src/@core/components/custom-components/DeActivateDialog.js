import axios from "axios";
import PropTypes from "prop-types";
import authConfig from "src/configs/auth";

import { Button, DialogContentText } from "@mui/material";
import Box from "@mui/material/Box";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";

import { getAuthorizationHeaders } from "src/helpers/utils";

const DeActivateDialog = ({ open, onClose, data, deleteEndpoint }) => {

  async function onClick(data) {
    try {
        const url = data?.isActive
        ? authConfig.baseURL + deleteEndpoint + "/deactivate" + "/" + data.id
        : authConfig.baseURL + deleteEndpoint + "/activate" +"/" + data.id;
  
      await axios.patch(
        url,
        {
          headers: getAuthorizationHeaders(),
        }
      );
    } catch (error) {
      console.error("Delete operation failed", error);
    }
    onClose();
  }

  return (
      <Dialog
        open={open}
        onClose={onClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              {data?.isActive ? "Are you sure, you want to deactivate ?":"Are you sure, you want to activate ?"}
            </DialogContentText>
          </DialogContent>
          <DialogActions> 
            <Button
              variant="contained"
              onClick={() => onClick(data)}
              sx={{ margin: "auto", width: 100 }}
            >
              Yes
            </Button>
            <Button
              variant="contained"
              onClick={onClose}
              sx={{ margin: "auto", width: 100 }}
            >
              No
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
  );
};

DeActivateDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  data: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    isActive: PropTypes.bool
  }).isRequired,
  deleteEndpoint: PropTypes.string.isRequired
};

export default DeActivateDialog;