import {
  getUrl,
  getAuthorizationHeaders,
} from 'src/helpers/utils.js';

// Mock dependencies
jest.mock('src/configs/auth', () => ({
  storageTokenKeyName: 'accessToken',
  storageUserKeyName: 'userData',
}));

// Mock window and localStorage
Object.defineProperty(window, 'location', {
  value: {
    hostname: 'localhost',
    href: 'http://localhost:3000',
  },
  writable: true,
});

describe('Utils Module', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    
    // Reset window location
    window.location.hostname = 'localhost';
    window.location.href = 'http://localhost:3000';
  });

  describe('getUrl Function', () => {


    test('should handle endpoints without leading slash', () => {
      window.location.hostname = 'localhost';
      
      const url = getUrl('api/test');
      
      expect(url).toBe('http://localhost:8080/pheart/api/test');
    });

    test('should handle empty endpoint', () => {
      window.location.hostname = 'localhost';
      
      const url = getUrl('');
      
      expect(url).toBe('http://localhost:8080/pheart/');
    });



  });

  describe('getAuthorizationHeaders Function', () => {
    test('should return headers with valid token', () => {
      localStorage.setItem('accessToken', 'valid-jwt-token');
      
      const headers = getAuthorizationHeaders();
      
      expect(headers).toEqual({
        'Authorization': 'Bearer valid-jwt-token',
        'Content-Type': 'application/json',
      });
    });




  });

  describe('Data Transformation Functions', () => {
    test('should format currency values', () => {
      const formatCurrency = (amount, currency = 'INR') => {
        return new Intl.NumberFormat('en-IN', {
          style: 'currency',
          currency: currency,
        }).format(amount);
      };
      
      expect(formatCurrency(1000)).toBe('₹1,000.00');
      expect(formatCurrency(1234.56)).toBe('₹1,234.56');
      expect(formatCurrency(0)).toBe('₹0.00');
    });

    test('should format dates consistently', () => {
      const formatDate = (dateString, format = 'dd/MM/yyyy') => {
        const date = new Date(dateString);
        
        if (format === 'dd/MM/yyyy') {
          return date.toLocaleDateString('en-GB');
        }
        
        return date.toLocaleDateString('en-IN');
      };
      
      expect(formatDate('2024-01-15')).toBe('15/01/2024');
    });

    test('should validate email addresses', () => {
      const isValidEmail = (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
      };
      
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('user@domain')).toBe(false);
      expect(isValidEmail('')).toBe(false);
    });


  });

  describe('Validation Algorithms', () => {
    test('should validate PAN number format', () => {
      const isValidPAN = (pan) => {
        const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
        return panRegex.test(pan);
      };
      
      expect(isValidPAN('**********')).toBe(true);
      expect(isValidPAN('abcde1234f')).toBe(false);
      expect(isValidPAN('ABCDE12345')).toBe(false);
      expect(isValidPAN('ABCD1234F')).toBe(false);
    });

    test('should validate Aadhaar number format', () => {
      const isValidAadhaar = (aadhaar) => {
        const cleanAadhaar = aadhaar.replace(/\D/g, '');
        return cleanAadhaar.length === 12 && /^\d{12}$/.test(cleanAadhaar);
      };
      
      expect(isValidAadhaar('123456789012')).toBe(true);
      expect(isValidAadhaar('1234 5678 9012')).toBe(true);
      expect(isValidAadhaar('12345678901')).toBe(false);
      expect(isValidAadhaar('abcd5678901')).toBe(false);
    });

    test('should validate GST number format', () => {
      const isValidGST = (gst) => {
        const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
        return gstRegex.test(gst);
      };
      
      expect(isValidGST('27**********1Z5')).toBe(true);
      expect(isValidGST('27abcde1234f1z5')).toBe(false);
      expect(isValidGST('27**********1Z')).toBe(false);
    });
  });

  describe('Formatting Logic', () => {
    test('should format file sizes', () => {
      const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      };
      
      expect(formatFileSize(0)).toBe('0 Bytes');
      expect(formatFileSize(1024)).toBe('1 KB');
      expect(formatFileSize(1048576)).toBe('1 MB');
      expect(formatFileSize(1073741824)).toBe('1 GB');
    });

    test('should format percentage values', () => {
      const formatPercentage = (value, total, decimals = 2) => {
        if (total === 0) return '0%';
        
        const percentage = (value / total) * 100;
        return `${percentage.toFixed(decimals)}%`;
      };
      
      expect(formatPercentage(25, 100)).toBe('25.00%');
      expect(formatPercentage(1, 3, 1)).toBe('33.3%');
      expect(formatPercentage(0, 100)).toBe('0.00%');
      expect(formatPercentage(50, 0)).toBe('0%');
    });

    test('should truncate text with ellipsis', () => {
      const truncateText = (text, maxLength) => {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength - 3) + '...';
      };
      
      expect(truncateText('Short text', 20)).toBe('Short text');
      expect(truncateText('This is a very long text', 10)).toBe('This is...');
      expect(truncateText('Exact', 5)).toBe('Exact');
    });
  });

  describe('Calculation Functions', () => {
    test('should calculate tax amounts', () => {
      const calculateTax = (amount, taxRate) => {
        return Math.round((amount * taxRate) * 100) / 100;
      };
      
      expect(calculateTax(1000, 0.18)).toBe(180);
      expect(calculateTax(1000, 0.05)).toBe(50);
      expect(calculateTax(1000, 0)).toBe(0);
    });

    test('should calculate compound interest', () => {
      const calculateCompoundInterest = (principal, rate, time, frequency = 1) => {
        const amount = principal * Math.pow((1 + rate / frequency), frequency * time);
        return Math.round((amount - principal) * 100) / 100;
      };
      
      expect(calculateCompoundInterest(1000, 0.1, 1)).toBe(100);
      expect(calculateCompoundInterest(1000, 0.1, 2)).toBe(210);
    });

    test('should calculate donation statistics', () => {
      const calculateStats = (donations) => {
        if (!donations || donations.length === 0) {
          return { total: 0, average: 0, count: 0 };
        }
        
        const total = donations.reduce((sum, donation) => sum + donation.amount, 0);
        const average = total / donations.length;
        
        return {
          total: Math.round(total * 100) / 100,
          average: Math.round(average * 100) / 100,
          count: donations.length,
        };
      };
      
      const donations = [
        { amount: 1000 },
        { amount: 2000 },
        { amount: 1500 },
      ];
      
      const stats = calculateStats(donations);
      
      expect(stats.total).toBe(4500);
      expect(stats.average).toBe(1500);
      expect(stats.count).toBe(3);
    });
  });

  describe('Error Handling Utilities', () => {
    test('should handle API error responses', () => {
      const handleApiError = (error) => {
        if (error.response) {
          return {
            message: error.response.data?.message || 'Server error',
            status: error.response.status,
            type: 'server',
          };
        }
        
        if (error.request) {
          return {
            message: 'Network error',
            status: 0,
            type: 'network',
          };
        }
        
        return {
          message: error.message || 'Unknown error',
          status: 0,
          type: 'unknown',
        };
      };
      
      const serverError = {
        response: {
          status: 500,
          data: { message: 'Internal server error' },
        },
      };
      
      const networkError = {
        request: {},
      };
      
      const unknownError = {
        message: 'Something went wrong',
      };
      
      expect(handleApiError(serverError)).toEqual({
        message: 'Internal server error',
        status: 500,
        type: 'server',
      });
      
      expect(handleApiError(networkError)).toEqual({
        message: 'Network error',
        status: 0,
        type: 'network',
      });
      
      expect(handleApiError(unknownError)).toEqual({
        message: 'Something went wrong',
        status: 0,
        type: 'unknown',
      });
    });

    test('should retry failed operations', async () => {
      const retryOperation = async (operation, maxRetries = 3, delay = 1000) => {
        let lastError;
        
        for (let i = 0; i <= maxRetries; i++) {
          try {
            return await operation();
          } catch (error) {
            lastError = error;
            
            if (i < maxRetries) {
              await new Promise(resolve => setTimeout(resolve, delay));
            }
          }
        }
        
        throw lastError;
      };
      
      let attempts = 0;
      const flakyOperation = () => {
        attempts++;
        if (attempts < 3) {
          throw new Error('Operation failed');
        }
        return 'Success';
      };
      
      const result = await retryOperation(flakyOperation, 3, 10);
      
      expect(result).toBe('Success');
      expect(attempts).toBe(3);
    });
  });

  describe('Performance Utilities', () => {
    test('should debounce function calls', (done) => {
      const debounce = (func, delay) => {
        let timeoutId;
        
        return (...args) => {
          clearTimeout(timeoutId);
          timeoutId = setTimeout(() => func.apply(null, args), delay);
        };
      };
      
      let callCount = 0;
      const testFunction = () => {
        callCount++;
      };
      
      const debouncedFunction = debounce(testFunction, 100);
      
      // Call multiple times rapidly
      debouncedFunction();
      debouncedFunction();
      debouncedFunction();
      
      // Should only be called once after delay
      setTimeout(() => {
        expect(callCount).toBe(1);
        done();
      }, 150);
    });

    test('should throttle function calls', (done) => {
      const throttle = (func, delay) => {
        let lastCall = 0;
        
        return (...args) => {
          const now = Date.now();
          
          if (now - lastCall >= delay) {
            lastCall = now;
            return func.apply(null, args);
          }
        };
      };
      
      let callCount = 0;
      const testFunction = () => {
        callCount++;
      };
      
      const throttledFunction = throttle(testFunction, 100);
      
      // Call multiple times
      throttledFunction();
      throttledFunction();
      throttledFunction();
      
      setTimeout(() => {
        throttledFunction();
        
        setTimeout(() => {
          expect(callCount).toBe(2);
          done();
        }, 50);
      }, 150);
    });
  });
});
