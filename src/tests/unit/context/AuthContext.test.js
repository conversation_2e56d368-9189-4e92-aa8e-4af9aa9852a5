import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { useRouter } from 'next/router';
import axios from 'axios';
import jwt from 'jsonwebtoken';
import { AuthProvider, AuthContext } from 'src/context/AuthContext';
import authConfig from 'src/configs/auth';

// Mock dependencies
jest.mock('axios');
jest.mock('jsonwebtoken');
jest.mock('src/configs/auth', () => ({
  storageTokenKeyName: 'accessToken',
  storageUserKeyName: 'userData',
  refreshTokenKeyName: 'refreshToken',
  onTokenExpiration: 'logout',
  individualPermissionsEndpoint: '/api/permissions',
  loginEndpoint: '/api/login',
  loginEndpointNew: '/api/login-new',
  allListNamesValues: '/api/list-values',
  listValues: 'listValues',
  listNames: 'listNames',
}));

jest.mock('src/helpers/utils', () => ({
  getUrl: jest.fn((endpoint) => `https://api.test.com${endpoint}`),
  getAuthorizationHeaders: jest.fn(() => ({
    'Authorization': 'Bearer test-token',
    'Content-Type': 'application/json',
  })),
}));

jest.mock('src/context/SessionExpirationDialog', () => {
  return function SessionExpirationDialog() {
    return <div data-testid="session-dialog">Session Dialog</div>;
  };
});

describe('AuthContext', () => {
  const mockPush = jest.fn();
  const mockReplace = jest.fn();
  const mockReload = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();

    // Mock useRouter since it's already mocked in setupTests.js
    const { useRouter } = require('next/router');
    useRouter.mockReturnValue({
      push: mockPush,
      replace: mockReplace,
      reload: mockReload,
      pathname: '/test',
      query: {},
      asPath: '/test',
      isReady: true,
      route: '/test',
    });

    // Mock axios defaults
    axios.get = jest.fn().mockResolvedValue({ data: {} });
    axios.post = jest.fn().mockResolvedValue({ data: {} });
    axios.patch = jest.fn().mockResolvedValue({ data: {} });
    axios.interceptors = {
      request: { use: jest.fn() },
      response: { use: jest.fn() },
    };

    // Mock JWT
    jwt.decode = jest.fn();
    jwt.verify = jest.fn();

    // Mock Date.now for consistent testing
    jest.spyOn(Date, 'now').mockReturnValue(1000000000);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  const TestComponent = () => {
    const auth = React.useContext(AuthContext);
    return (
      <div>
        <div data-testid="user">{auth.user ? 'logged-in' : 'logged-out'}</div>
        <div data-testid="loading">{auth.loading ? 'loading' : 'not-loading'}</div>
        <button onClick={() => auth.login({}, jest.fn(), jest.fn())}>Login</button>
        <button onClick={auth.logout}>Logout</button>
      </div>
    );
  };

  const renderAuthProvider = (children = <TestComponent />) => {
    return render(
      <AuthProvider>
        {children}
      </AuthProvider>
    );
  };

  describe('Initial State Tests', () => {
    test('should initialize with null user state', () => {
      renderAuthProvider();
      expect(screen.getByTestId('user')).toHaveTextContent('logged-out');
    });

    test('should initialize with loading true', () => {
      renderAuthProvider();
      expect(screen.getByTestId('loading')).toHaveTextContent('loading');
    });

    test('should provide default context values', () => {
      const TestDefaultValues = () => {
        const auth = React.useContext(AuthContext);
        return (
          <div>
            <div data-testid="topMenuData">{auth.topMenuData || 'null'}</div>
            <div data-testid="leftMenuData">{auth.leftMenuData || 'null'}</div>
            <div data-testid="quickLinksData">{auth.quickLinksData || 'null'}</div>
          </div>
        );
      };

      render(
        <AuthProvider>
          <TestDefaultValues />
        </AuthProvider>
      );

      expect(screen.getByTestId('topMenuData')).toHaveTextContent('null');
      expect(screen.getByTestId('leftMenuData')).toHaveTextContent('null');
      expect(screen.getByTestId('quickLinksData')).toHaveTextContent('null');
    });
  });

  describe('Token Management Tests', () => {





    test('should handle token expiration logic', () => {
      const expiredToken = 'expired-token';
      const currentTime = Date.now();
      const expiredTime = (currentTime / 1000) - 3600; // 1 hour ago

      localStorage.setItem('accessToken', expiredToken);
      jwt.decode.mockReturnValue({ exp: expiredTime });

      renderAuthProvider();

      // Token expiration logic should be triggered
      expect(jwt.decode).toHaveBeenCalledWith(expiredToken);
    });

    test('should validate token format', () => {
      const validToken = 'valid.jwt.token';
      const invalidToken = 'invalid-token';

      jwt.decode.mockImplementation((token) => {
        if (token === validToken) {
          return { exp: Date.now() / 1000 + 3600 };
        }
        throw new Error('Invalid token');
      });

      localStorage.setItem('accessToken', validToken);
      renderAuthProvider();

      expect(jwt.decode).toHaveBeenCalledWith(validToken);
    });
  });

  describe('User State Management Tests', () => {

    test('should clear user state on logout', async () => {
      const TestUserClear = () => {
        const auth = React.useContext(AuthContext);
        const [userCleared, setUserCleared] = React.useState(false);

        const handleLogout = () => {
          auth.logout();
          setUserCleared(auth.user === null);
        };

        return (
          <div>
            <button onClick={handleLogout} data-testid="logout-btn">Logout</button>
            <div data-testid="user-cleared">{userCleared ? 'cleared' : 'not-cleared'}</div>
          </div>
        );
      };

      render(
        <AuthProvider>
          <TestUserClear />
        </AuthProvider>
      );

      const logoutButton = screen.getByTestId('logout-btn');
      await act(async () => {
        logoutButton.click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('user-cleared')).toHaveTextContent('cleared');
      });
    });

    test('should handle loading states correctly', async () => {
      const TestLoadingStates = () => {
        const auth = React.useContext(AuthContext);
        
        React.useEffect(() => {
          auth.setLoading(false);
        }, []);

        return (
          <div data-testid="loading-state">
            {auth.loading ? 'loading' : 'not-loading'}
          </div>
        );
      };

      render(
        <AuthProvider>
          <TestLoadingStates />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading-state')).toHaveTextContent('not-loading');
      });
    });
  });

  describe('Authentication Flow Tests', () => {
    test('should handle successful login flow', async () => {
      const mockLoginResponse = {
        data: {
          token: {
            accessToken: 'new-access-token',
            refreshToken: 'new-refresh-token',
          },
        },
      };

      const mockUserResponse = {
        data: {
          id: '1',
          email: '<EMAIL>',
          role: 'admin',
        },
      };

      axios.post.mockResolvedValue(mockLoginResponse);
      axios.get.mockResolvedValue(mockUserResponse);
      jwt.verify.mockImplementation((token, secret, options, callback) => {
        callback(null, { exp: Date.now() / 1000 + 3600 });
      });

      const TestSuccessfulLogin = () => {
        const auth = React.useContext(AuthContext);
        const [loginResult, setLoginResult] = React.useState('');

        const handleLogin = async () => {
          try {
            await auth.loginNew(
              { email: '<EMAIL>', password: 'password' },
              () => setLoginResult('error'),
              () => setLoginResult('success'),
              () => setLoginResult('popup')
            );
          } catch (error) {
            setLoginResult('error');
          }
        };

        return (
          <div>
            <button onClick={handleLogin} data-testid="login-btn">Login</button>
            <div data-testid="login-result">{loginResult}</div>
          </div>
        );
      };

      render(
        <AuthProvider>
          <TestSuccessfulLogin />
        </AuthProvider>
      );

      const loginButton = screen.getByTestId('login-btn');
      await act(async () => {
        loginButton.click();
      });

      await waitFor(() => {
        expect(axios.post).toHaveBeenCalledWith(
          'https://api.test.com/api/login',
          { email: '<EMAIL>', password: 'password' }
        );
      });
    });

    test('should handle login errors', async () => {
      const mockError = new Error('Login failed');
      axios.post.mockRejectedValue(mockError);

      const TestLoginError = () => {
        const auth = React.useContext(AuthContext);
        const [errorOccurred, setErrorOccurred] = React.useState(false);

        const handleLogin = async () => {
          await auth.loginNew(
            { email: '<EMAIL>', password: 'wrong' },
            () => setErrorOccurred(true),
            () => {},
            () => {}
          );
        };

        return (
          <div>
            <button onClick={handleLogin} data-testid="login-btn">Login</button>
            <div data-testid="error-state">{errorOccurred ? 'error' : 'no-error'}</div>
          </div>
        );
      };

      render(
        <AuthProvider>
          <TestLoginError />
        </AuthProvider>
      );

      const loginButton = screen.getByTestId('login-btn');
      await act(async () => {
        loginButton.click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('error-state')).toHaveTextContent('error');
      });
    });


  });

  describe('API Integration Tests', () => {
    test('should fetch list values on user login', async () => {
      const mockListValuesResponse = {
        data: {
          data: [{ id: 1, name: 'Test Value' }],
        },
      };

      axios.post.mockResolvedValue(mockListValuesResponse);

      const TestListValues = () => {
        const auth = React.useContext(AuthContext);
        
        React.useEffect(() => {
          if (auth.user) {
            // List values should be fetched when user is set
          }
        }, [auth.user]);

        return <div data-testid="list-values-test">Test</div>;
      };

      render(
        <AuthProvider>
          <TestListValues />
        </AuthProvider>
      );

      // Simulate user being set
      await act(async () => {
        // This would trigger the useEffect that fetches list values
      });

      expect(screen.getByTestId('list-values-test')).toBeInTheDocument();
    });

    test('should handle API errors gracefully', async () => {
      const mockError = new Error('API Error');
      axios.get.mockRejectedValue(mockError);

      const TestAPIError = () => {
        const auth = React.useContext(AuthContext);
        const [errorHandled, setErrorHandled] = React.useState(false);

        React.useEffect(() => {
          // Simulate API call that fails
          axios.get('/api/test').catch(() => {
            setErrorHandled(true);
          });
        }, []);

        return (
          <div data-testid="api-error-handled">
            {errorHandled ? 'handled' : 'not-handled'}
          </div>
        );
      };

      render(
        <AuthProvider>
          <TestAPIError />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('api-error-handled')).toHaveTextContent('handled');
      });
    });
  });

  describe('Session Management Tests', () => {
    test('should handle session expiration warning', () => {
      const futureTime = Date.now() + (6 * 60 * 1000); // 6 minutes from now
      const token = 'valid-token';
      
      localStorage.setItem('accessToken', token);
      jwt.decode.mockReturnValue({ exp: futureTime / 1000 });

      renderAuthProvider();

      expect(jwt.decode).toHaveBeenCalledWith(token);
    });

    test('should redirect to login on session expiration', () => {
      const pastTime = Date.now() - (60 * 1000); // 1 minute ago
      const token = 'expired-token';
      
      localStorage.setItem('accessToken', token);
      jwt.decode.mockReturnValue({ exp: pastTime / 1000 });

      renderAuthProvider();

      // Session expiration logic should be triggered
      expect(jwt.decode).toHaveBeenCalledWith(token);
    });
  });
});
