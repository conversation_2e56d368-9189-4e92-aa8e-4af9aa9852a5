import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useRouter } from 'next/router';
import LoginPage from 'src/pages/login/index.js';
import { useAuth } from 'src/hooks/useAuth';
import { AuthContext } from 'src/context/AuthContext';
import { fetchIpAddress } from 'src/@core/components/custom-components/FetchIpAddress';

// Mock dependencies
jest.mock('src/hooks/useAuth');
jest.mock('src/@core/components/custom-components/FetchIpAddress');
jest.mock('src/configs/auth', () => ({
  googleAuthUrl: 'https://google.com/auth',
  guestURL: 'https://guest.com/',
}));

// Mock BlankLayout
jest.mock('src/@core/layouts/BlankLayout', () => {
  return function BlankLayout({ children }) {
    return <div data-testid="blank-layout">{children}</div>;
  };
});

// Mock FallbackSpinner
jest.mock('src/@core/components/spinner', () => {
  return function FallbackSpinner() {
    return <div data-testid="fallback-spinner">Loading...</div>;
  };
});

// Mock Icon component
jest.mock('src/@core/components/icon', () => {
  return function Icon({ icon }) {
    return <span data-testid="icon">{icon}</span>;
  };
});

describe('LoginPage', () => {
  const mockPush = jest.fn();
  const mockReplace = jest.fn();
  const mockLoginNew = jest.fn();
  const mockAuth = {
    loginNew: mockLoginNew,
    user: null,
    loading: false,
  };

  const mockAuthContextValue = {
    loginLoad: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock useRouter since it's already mocked in setupTests.js
    const { useRouter } = require('next/router');
    useRouter.mockReturnValue({
      push: mockPush,
      replace: mockReplace,
      pathname: '/login',
      query: {},
      asPath: '/login',
      isReady: true,
      route: '/login',
    });

    useAuth.mockReturnValue(mockAuth);
    fetchIpAddress.mockResolvedValue('***********');
  });

  const renderLoginPage = (contextValue = mockAuthContextValue) => {
    return render(
      <AuthContext.Provider value={contextValue}>
        <LoginPage />
      </AuthContext.Provider>
    );
  };

  describe('Rendering Tests', () => {
    test('should render login form with email and password fields', () => {
      renderLoginPage();
      
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /login/i })).toBeInTheDocument();
    });

    test('should render welcome message and logo', () => {
      renderLoginPage();
      
      expect(screen.getByText(/welcome to pure heart/i)).toBeInTheDocument();
      expect(screen.getByAltText(/pure heart logo/i)).toBeInTheDocument();
    });

    test('should render forgot password link', () => {
      renderLoginPage();
      
      expect(screen.getByText(/forgot password/i)).toBeInTheDocument();
    });

    test('should render create account options', () => {
      renderLoginPage();
      
      expect(screen.getByText(/new on pure heart/i)).toBeInTheDocument();
      expect(screen.getByText(/create account/i)).toBeInTheDocument();
    });

    test('should show loading spinner when loginLoad is true', () => {
      renderLoginPage({ loginLoad: true });
      
      expect(screen.getByTestId('fallback-spinner')).toBeInTheDocument();
      expect(screen.queryByLabelText(/email/i)).not.toBeInTheDocument();
    });
  });

  describe('Form Validation Tests', () => {
    test('should validate email format', async () => {
      const user = userEvent.setup();
      renderLoginPage();
      
      const emailInput = screen.getByLabelText(/email/i);
      const submitButton = screen.getByRole('button', { name: /login/i });
      
      await user.type(emailInput, 'invalid-email');
      await user.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/enter a valid email address/i)).toBeInTheDocument();
      });
    });

    test('should require email field', async () => {
      const user = userEvent.setup();
      renderLoginPage();
      
      const submitButton = screen.getByRole('button', { name: /login/i });
      await user.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      });
    });

    test('should require password field', async () => {
      const user = userEvent.setup();
      renderLoginPage();
      
      const emailInput = screen.getByLabelText(/email/i);
      const submitButton = screen.getByRole('button', { name: /login/i });
      
      await user.type(emailInput, '<EMAIL>');
      await user.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/password is required/i)).toBeInTheDocument();
      });
    });

    test('should handle empty form submission', async () => {
      const user = userEvent.setup();
      renderLoginPage();
      
      const submitButton = screen.getByRole('button', { name: /login/i });
      await user.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/email is required/i)).toBeInTheDocument();
        expect(screen.getByText(/password is required/i)).toBeInTheDocument();
      });
    });
  });

  describe('Password Visibility Tests', () => {
    test('should toggle password visibility', async () => {
      const user = userEvent.setup();
      renderLoginPage();
      
      const passwordInput = screen.getByLabelText(/password/i);
      const toggleButton = screen.getByTestId('icon').parentElement;
      
      expect(passwordInput).toHaveAttribute('type', 'password');
      
      await user.click(toggleButton);
      expect(passwordInput).toHaveAttribute('type', 'text');
      
      await user.click(toggleButton);
      expect(passwordInput).toHaveAttribute('type', 'password');
    });
  });

  describe('Navigation Tests', () => {
    test('should navigate to forgot password page', async () => {
      const user = userEvent.setup();
      renderLoginPage();
      
      const forgotPasswordLink = screen.getByText(/forgot password/i);
      await user.click(forgotPasswordLink);
      
      expect(mockPush).toHaveBeenCalledWith('/forgot-password');
    });

    test('should show registration menu on create account click', async () => {
      const user = userEvent.setup();
      renderLoginPage();
      
      const createAccountButton = screen.getByText(/create account/i);
      await user.click(createAccountButton);
      
      await waitFor(() => {
        expect(screen.getByText(/sign up as ngo/i)).toBeInTheDocument();
        expect(screen.getByText(/sign up as donor/i)).toBeInTheDocument();
      });
    });

    test('should navigate to NGO registration', async () => {
      const user = userEvent.setup();
      renderLoginPage();
      
      const createAccountButton = screen.getByText(/create account/i);
      await user.click(createAccountButton);
      
      await waitFor(() => {
        const ngoOption = screen.getByText(/sign up as ngo/i);
        user.click(ngoOption);
      });
      
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/register?role=ngo');
      });
    });

    test('should navigate to Donor registration', async () => {
      const user = userEvent.setup();
      renderLoginPage();
      
      const createAccountButton = screen.getByText(/create account/i);
      await user.click(createAccountButton);
      
      await waitFor(() => {
        const donorOption = screen.getByText(/sign up as donor/i);
        user.click(donorOption);
      });
      
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/register?role=donor');
      });
    });
  });

  describe('Login Submission Tests', () => {
    test('should submit form with valid credentials', async () => {
      const user = userEvent.setup();
      mockLoginNew.mockResolvedValue();
      renderLoginPage();
      
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /login/i });
      
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);
      
      await waitFor(() => {
        expect(mockLoginNew).toHaveBeenCalledWith(
          {
            email: '<EMAIL>',
            password: 'password123',
            ipAddress: '***********',
            overrideExistingLogins: false,
          },
          expect.any(Function),
          expect.any(Function),
          expect.any(Function)
        );
      });
    });

    test('should handle login failure', async () => {
      const user = userEvent.setup();
      mockLoginNew.mockImplementation((_, errorCallback) => {
        errorCallback(new Error('Invalid credentials'));
      });
      renderLoginPage();
      
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /login/i });
      
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrongpassword');
      await user.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/email or password credentials are invalid/i)).toBeInTheDocument();
      });
    });

    test('should handle login success', async () => {
      const user = userEvent.setup();
      mockLoginNew.mockImplementation((_, __, successCallback) => {
        successCallback();
      });
      renderLoginPage();
      
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /login/i });
      
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/login successful/i)).toBeInTheDocument();
      });
    });

    test('should handle multiple device login popup', async () => {
      const user = userEvent.setup();
      mockLoginNew.mockImplementation((_, __, ___, handlePopup) => {
        handlePopup();
      });
      renderLoginPage();
      
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /login/i });
      
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/you are currently logged in on multiple devices/i)).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling Tests', () => {


    test('should handle dialog close', async () => {
      const user = userEvent.setup();
      mockLoginNew.mockImplementation((_, errorCallback) => {
        errorCallback(new Error('Invalid credentials'));
      });
      renderLoginPage();
      
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /login/i });
      
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrongpassword');
      await user.click(submitButton);
      
      await waitFor(() => {
        const okayButton = screen.getByText(/okay/i);
        user.click(okayButton);
      });
      
      await waitFor(() => {
        expect(screen.queryByText(/email or password credentials are invalid/i)).not.toBeInTheDocument();
      });
    });
  });

  describe('Override Existing Logins Tests', () => {
    test('should handle override existing logins confirmation', async () => {
      const user = userEvent.setup();
      mockLoginNew.mockImplementation((_, __, ___, handlePopup) => {
        handlePopup();
      });
      renderLoginPage();
      
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /login/i });
      
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);
      
      await waitFor(() => {
        const yesButton = screen.getByText(/yes/i);
        user.click(yesButton);
      });
      
      await waitFor(() => {
        expect(mockLoginNew).toHaveBeenCalledWith(
          expect.objectContaining({
            overrideExistingLogins: true,
          }),
          expect.any(Function),
          expect.any(Function),
          expect.any(Function)
        );
      });
    });
  });
});
