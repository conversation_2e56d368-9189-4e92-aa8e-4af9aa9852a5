import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useRouter } from 'next/router';
import axios from 'axios';
import Donors from 'src/pages/donors/index';
import { AuthProvider, AuthContext } from 'src/context/AuthContext';
import { useRBAC } from 'src/pages/permission/RBACContext';
import authConfig from 'src/configs/auth';

// Mock dependencies
jest.mock('axios');
jest.mock('src/helpers/utils', () => ({
  getUrl: jest.fn((endpoint) => `https://api.test.com${endpoint}`),
  getAuthorizationHeaders: jest.fn(() => ({
    'Authorization': 'Bearer test-token',
    'Content-Type': 'application/json',
  })),
}));

jest.mock('src/pages/permission/RBACContext', () => ({
  useRBAC: jest.fn(),
}));

jest.mock('src/@core/components/custom-components/NavTabsDonors', () => {
  return function MockNavTabsDonors({ tabContent1, tabContent2 }) {
    const [activeTab, setActiveTab] = React.useState(0);
    
    return (
      <div data-testid="nav-tabs-donors">
        <div data-testid="tabs-header">
          <button 
            data-testid="tab-0"
            onClick={() => setActiveTab(0)}
            className={activeTab === 0 ? 'active' : ''}
          >
            Donors
          </button>
          <button 
            data-testid="tab-1"
            onClick={() => setActiveTab(1)}
            className={activeTab === 1 ? 'active' : ''}
          >
            Contact Groups
          </button>
        </div>
        <div data-testid="tab-content">
          {activeTab === 0 && tabContent1}
          {activeTab === 1 && tabContent2}
        </div>
      </div>
    );
  };
});

jest.mock('src/pages/donors/DonorsPage', () => {
  return function MockDonorsPage() {
    const [donors, setDonors] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [searchKeyword, setSearchKeyword] = React.useState('');
    const [openDialog, setOpenDialog] = React.useState(false);
    const [openDeleteDialog, setOpenDeleteDialog] = React.useState(false);

    React.useEffect(() => {
      // Simulate API call
      setTimeout(() => {
        setDonors([
          { id: 1, name: 'John Donor', email: '<EMAIL>', phone: '**********' },
          { id: 2, name: 'Jane Donor', email: '<EMAIL>', phone: '**********' },
        ]);
        setLoading(false);
      }, 100);
    }, []);

    return (
      <div data-testid="donors-page">
        <div data-testid="donors-header">
          <h2>Donors Management</h2>
          <div data-testid="action-buttons">
            <button onClick={() => setOpenDialog(true)}>Add Donor</button>
            <button data-testid="import-btn">Import</button>
            <button data-testid="export-btn">Export</button>
          </div>
        </div>
        
        <div data-testid="search-section">
          <input
            data-testid="search-input"
            placeholder="Search donors..."
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
          />
          <button data-testid="advanced-search-btn">Advanced Search</button>
        </div>

        {loading ? (
          <div data-testid="loading-spinner">Loading...</div>
        ) : (
          <div data-testid="donors-grid">
            <div data-testid="grid-header">
              <span>Name</span>
              <span>Email</span>
              <span>Phone</span>
              <span>Actions</span>
            </div>
            {donors
              .filter(donor => 
                !searchKeyword || 
                donor.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
                donor.email.toLowerCase().includes(searchKeyword.toLowerCase())
              )
              .map(donor => (
                <div key={donor.id} data-testid={`donor-row-${donor.id}`}>
                  <span>{donor.name}</span>
                  <span>{donor.email}</span>
                  <span>{donor.phone}</span>
                  <div>
                    <button data-testid={`edit-${donor.id}`}>Edit</button>
                    <button data-testid={`delete-${donor.id}`} onClick={() => setOpenDeleteDialog(true)}>Delete</button>
                    <button data-testid={`whatsapp-${donor.id}`}>WhatsApp</button>
                    <button data-testid={`email-${donor.id}`}>Email</button>
                  </div>
                </div>
              ))
            }
          </div>
        )}

        {openDialog && (
          <div data-testid="donor-dialog">
            <h3>Add/Edit Donor</h3>
            <input data-testid="donor-name-input" placeholder="Name" />
            <input data-testid="donor-email-input" placeholder="Email" />
            <button onClick={() => setOpenDialog(false)}>Cancel</button>
            <button data-testid="save-donor">Save</button>
          </div>
        )}

        {openDeleteDialog && (
          <div data-testid="delete-dialog">
            <h3>Confirm Delete</h3>
            <p>Are you sure you want to delete this donor?</p>
            <button onClick={() => setOpenDeleteDialog(false)}>Cancel</button>
            <button data-testid="confirm-delete">Delete</button>
          </div>
        )}
      </div>
    );
  };
});

jest.mock('src/pages/contact-groups', () => {
  return function MockContactGroupActions() {
    return (
      <div data-testid="contact-groups">
        <h2>Contact Groups</h2>
        <p>Contact groups management functionality</p>
      </div>
    );
  };
});

describe('Donors Page', () => {
  const mockPush = jest.fn();
  const mockReplace = jest.fn();
  const mockAuthContext = {
    user: {
      id: 1,
      organisationCategory: 'NGO',
      role: 'admin',
    },
    donorDetails: null,
    setDonorDetails: jest.fn(),
    getAllListValuesByListNameId: jest.fn(),
    loading: false,
  };

  const mockRBAC = {
    canMenuPage: jest.fn(() => true),
    canMenuPageSection: jest.fn(() => true),
    canMenuPageSectionField: jest.fn(() => true),
    rbacRoles: ['admin'],
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock useRouter
    const { useRouter } = require('next/router');
    useRouter.mockReturnValue({
      push: mockPush,
      replace: mockReplace,
      query: {},
      pathname: '/donors',
      asPath: '/donors',
    });

    // Mock useRBAC
    useRBAC.mockReturnValue(mockRBAC);

    // Mock axios
    axios.mockImplementation(() => Promise.resolve({ 
      data: {
        donorsResponseList: [
          { id: 1, name: 'John Donor', email: '<EMAIL>', phone: '**********' },
          { id: 2, name: 'Jane Donor', email: '<EMAIL>', phone: '**********' },
        ],
        rowCount: 2,
      }
    }));
  });

  const renderDonors = (contextOverrides = {}) => {
    const authContextValue = { ...mockAuthContext, ...contextOverrides };
    
    return render(
      <AuthProvider value={authContextValue}>
        <Donors />
      </AuthProvider>
    );
  };

  describe('Permission Tests', () => {
    test('should render donors page when user has read permission', () => {
      renderDonors();

      expect(screen.getByTestId('nav-tabs-donors')).toBeInTheDocument();
      expect(screen.getByTestId('donors-page')).toBeInTheDocument();
    });

    test('should redirect to 401 when user lacks read permission', async () => {
      const restrictedRBAC = {
        ...mockRBAC,
        canMenuPage: jest.fn(() => false),
      };
      useRBAC.mockReturnValue(restrictedRBAC);

      renderDonors();

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/401');
      });
    });

    test('should return null when user lacks permission', () => {
      const restrictedRBAC = {
        ...mockRBAC,
        canMenuPage: jest.fn(() => false),
        rbacRoles: [],
      };
      useRBAC.mockReturnValue(restrictedRBAC);

      const { container } = renderDonors();
      expect(container.firstChild).toBeNull();
    });

    test('should check permissions when rbacRoles change', async () => {
      const { rerender } = renderDonors();

      const updatedRBAC = {
        ...mockRBAC,
        canMenuPage: jest.fn(() => false),
        rbacRoles: ['restricted'],
      };
      useRBAC.mockReturnValue(updatedRBAC);

      rerender(
        <AuthProvider value={mockAuthContext}>
          <Donors />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/401');
      });
    });
  });

  describe('Tab Navigation Tests', () => {
    test('should render both tab headers', () => {
      renderDonors();

      expect(screen.getByTestId('tab-0')).toBeInTheDocument();
      expect(screen.getByTestId('tab-1')).toBeInTheDocument();
      expect(screen.getByText('Donors')).toBeInTheDocument();
      expect(screen.getByText('Contact Groups')).toBeInTheDocument();
    });

    test('should show donors page by default', () => {
      renderDonors();

      expect(screen.getByTestId('donors-page')).toBeInTheDocument();
      expect(screen.queryByTestId('contact-groups')).not.toBeInTheDocument();
    });

    test('should switch to contact groups tab', async () => {
      const user = userEvent.setup();
      renderDonors();

      const contactGroupsTab = screen.getByTestId('tab-1');
      await user.click(contactGroupsTab);

      expect(screen.getByTestId('contact-groups')).toBeInTheDocument();
      expect(screen.queryByTestId('donors-page')).not.toBeInTheDocument();
    });

    test('should switch back to donors tab', async () => {
      const user = userEvent.setup();
      renderDonors();

      // Switch to contact groups
      const contactGroupsTab = screen.getByTestId('tab-1');
      await user.click(contactGroupsTab);

      // Switch back to donors
      const donorsTab = screen.getByTestId('tab-0');
      await user.click(donorsTab);

      expect(screen.getByTestId('donors-page')).toBeInTheDocument();
      expect(screen.queryByTestId('contact-groups')).not.toBeInTheDocument();
    });
  });

  describe('Donors Page Content Tests', () => {
    test('should render donors management header', () => {
      renderDonors();

      expect(screen.getByText('Donors Management')).toBeInTheDocument();
    });

    test('should render action buttons', () => {
      renderDonors();

      expect(screen.getByText('Add Donor')).toBeInTheDocument();
      expect(screen.getByTestId('import-btn')).toBeInTheDocument();
      expect(screen.getByTestId('export-btn')).toBeInTheDocument();
    });

    test('should render search functionality', () => {
      renderDonors();

      expect(screen.getByTestId('search-input')).toBeInTheDocument();
      expect(screen.getByTestId('advanced-search-btn')).toBeInTheDocument();
    });

    test('should show loading state initially', () => {
      renderDonors();

      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });

    test('should display donors grid after loading', async () => {
      renderDonors();

      await waitFor(() => {
        expect(screen.getByTestId('donors-grid')).toBeInTheDocument();
      });

      expect(screen.getByTestId('donor-row-1')).toBeInTheDocument();
      expect(screen.getByTestId('donor-row-2')).toBeInTheDocument();
      expect(screen.getByText('John Donor')).toBeInTheDocument();
      expect(screen.getByText('Jane Donor')).toBeInTheDocument();
    });
  });

  describe('Search Functionality Tests', () => {
    test('should filter donors by name', async () => {
      const user = userEvent.setup();
      renderDonors();

      await waitFor(() => {
        expect(screen.getByTestId('donors-grid')).toBeInTheDocument();
      });

      const searchInput = screen.getByTestId('search-input');
      await user.type(searchInput, 'John');

      expect(screen.getByTestId('donor-row-1')).toBeInTheDocument();
      expect(screen.queryByTestId('donor-row-2')).not.toBeInTheDocument();
    });

    test('should filter donors by email', async () => {
      const user = userEvent.setup();
      renderDonors();

      await waitFor(() => {
        expect(screen.getByTestId('donors-grid')).toBeInTheDocument();
      });

      const searchInput = screen.getByTestId('search-input');
      await user.type(searchInput, '<EMAIL>');

      expect(screen.queryByTestId('donor-row-1')).not.toBeInTheDocument();
      expect(screen.getByTestId('donor-row-2')).toBeInTheDocument();
    });

    test('should show no results when search doesn\'t match', async () => {
      const user = userEvent.setup();
      renderDonors();

      await waitFor(() => {
        expect(screen.getByTestId('donors-grid')).toBeInTheDocument();
      });

      const searchInput = screen.getByTestId('search-input');
      await user.type(searchInput, 'nonexistent');

      expect(screen.queryByTestId('donor-row-1')).not.toBeInTheDocument();
      expect(screen.queryByTestId('donor-row-2')).not.toBeInTheDocument();
    });

    test('should clear search and show all donors', async () => {
      const user = userEvent.setup();
      renderDonors();

      await waitFor(() => {
        expect(screen.getByTestId('donors-grid')).toBeInTheDocument();
      });

      const searchInput = screen.getByTestId('search-input');
      await user.type(searchInput, 'John');
      await user.clear(searchInput);

      expect(screen.getByTestId('donor-row-1')).toBeInTheDocument();
      expect(screen.getByTestId('donor-row-2')).toBeInTheDocument();
    });
  });

  describe('CRUD Operations Tests', () => {
    test('should open add donor dialog', async () => {
      const user = userEvent.setup();
      renderDonors();

      const addButton = screen.getByText('Add Donor');
      await user.click(addButton);

      expect(screen.getByTestId('donor-dialog')).toBeInTheDocument();
      expect(screen.getByText('Add/Edit Donor')).toBeInTheDocument();
    });

    test('should close donor dialog', async () => {
      const user = userEvent.setup();
      renderDonors();

      // Open dialog
      const addButton = screen.getByText('Add Donor');
      await user.click(addButton);

      // Close dialog
      const cancelButton = screen.getByText('Cancel');
      await user.click(cancelButton);

      expect(screen.queryByTestId('donor-dialog')).not.toBeInTheDocument();
    });

    test('should handle edit donor action', async () => {
      const user = userEvent.setup();
      renderDonors();

      await waitFor(() => {
        expect(screen.getByTestId('donors-grid')).toBeInTheDocument();
      });

      const editButton = screen.getByTestId('edit-1');
      await user.click(editButton);

      // Should trigger edit functionality
      expect(editButton).toHaveBeenClicked;
    });

    test('should open delete confirmation dialog', async () => {
      const user = userEvent.setup();
      renderDonors();

      await waitFor(() => {
        expect(screen.getByTestId('donors-grid')).toBeInTheDocument();
      });

      const deleteButton = screen.getByTestId('delete-1');
      await user.click(deleteButton);

      expect(screen.getByTestId('delete-dialog')).toBeInTheDocument();
      expect(screen.getByText('Confirm Delete')).toBeInTheDocument();
    });

    test('should confirm donor deletion', async () => {
      const user = userEvent.setup();
      renderDonors();

      await waitFor(() => {
        expect(screen.getByTestId('donors-grid')).toBeInTheDocument();
      });

      // Open delete dialog
      const deleteButton = screen.getByTestId('delete-1');
      await user.click(deleteButton);

      // Confirm deletion
      const confirmButton = screen.getByTestId('confirm-delete');
      await user.click(confirmButton);

      // Should trigger delete functionality
      expect(confirmButton).toHaveBeenClicked;
    });

    test('should cancel donor deletion', async () => {
      const user = userEvent.setup();
      renderDonors();

      await waitFor(() => {
        expect(screen.getByTestId('donors-grid')).toBeInTheDocument();
      });

      // Open delete dialog
      const deleteButton = screen.getByTestId('delete-1');
      await user.click(deleteButton);

      // Cancel deletion
      const cancelButton = screen.getByText('Cancel');
      await user.click(cancelButton);

      expect(screen.queryByTestId('delete-dialog')).not.toBeInTheDocument();
    });
  });

  describe('Communication Features Tests', () => {
    test('should handle WhatsApp action', async () => {
      const user = userEvent.setup();
      renderDonors();

      await waitFor(() => {
        expect(screen.getByTestId('donors-grid')).toBeInTheDocument();
      });

      const whatsappButton = screen.getByTestId('whatsapp-1');
      await user.click(whatsappButton);

      // Should trigger WhatsApp functionality
      expect(whatsappButton).toHaveBeenClicked;
    });

    test('should handle email action', async () => {
      const user = userEvent.setup();
      renderDonors();

      await waitFor(() => {
        expect(screen.getByTestId('donors-grid')).toBeInTheDocument();
      });

      const emailButton = screen.getByTestId('email-1');
      await user.click(emailButton);

      // Should trigger email functionality
      expect(emailButton).toHaveBeenClicked;
    });
  });

  describe('Import/Export Tests', () => {
    test('should handle import action', async () => {
      const user = userEvent.setup();
      renderDonors();

      const importButton = screen.getByTestId('import-btn');
      await user.click(importButton);

      // Should trigger import functionality
      expect(importButton).toHaveBeenClicked;
    });

    test('should handle export action', async () => {
      const user = userEvent.setup();
      renderDonors();

      const exportButton = screen.getByTestId('export-btn');
      await user.click(exportButton);

      // Should trigger export functionality
      expect(exportButton).toHaveBeenClicked;
    });
  });

  describe('Advanced Search Tests', () => {
    test('should open advanced search', async () => {
      const user = userEvent.setup();
      renderDonors();

      const advancedSearchButton = screen.getByTestId('advanced-search-btn');
      await user.click(advancedSearchButton);

      // Should trigger advanced search functionality
      expect(advancedSearchButton).toHaveBeenClicked;
    });
  });

  describe('Form Validation Tests', () => {
    test('should validate donor form fields', async () => {
      const user = userEvent.setup();
      renderDonors();

      // Open add donor dialog
      const addButton = screen.getByText('Add Donor');
      await user.click(addButton);

      // Fill form
      const nameInput = screen.getByTestId('donor-name-input');
      const emailInput = screen.getByTestId('donor-email-input');

      await user.type(nameInput, 'New Donor');
      await user.type(emailInput, '<EMAIL>');

      // Save donor
      const saveButton = screen.getByTestId('save-donor');
      await user.click(saveButton);

      // Should trigger save functionality
      expect(saveButton).toHaveBeenClicked;
    });
  });

  describe('Grid Display Tests', () => {
    test('should display donor data in grid', async () => {
      renderDonors();

      await waitFor(() => {
        expect(screen.getByTestId('donors-grid')).toBeInTheDocument();
      });

      expect(screen.getByText('John Donor')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('**********')).toBeInTheDocument();

      expect(screen.getByText('Jane Donor')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('**********')).toBeInTheDocument();
    });

    test('should display action buttons for each donor', async () => {
      renderDonors();

      await waitFor(() => {
        expect(screen.getByTestId('donors-grid')).toBeInTheDocument();
      });

      // Check action buttons for first donor
      expect(screen.getByTestId('edit-1')).toBeInTheDocument();
      expect(screen.getByTestId('delete-1')).toBeInTheDocument();
      expect(screen.getByTestId('whatsapp-1')).toBeInTheDocument();
      expect(screen.getByTestId('email-1')).toBeInTheDocument();

      // Check action buttons for second donor
      expect(screen.getByTestId('edit-2')).toBeInTheDocument();
      expect(screen.getByTestId('delete-2')).toBeInTheDocument();
      expect(screen.getByTestId('whatsapp-2')).toBeInTheDocument();
      expect(screen.getByTestId('email-2')).toBeInTheDocument();
    });
  });

  describe('Error Handling Tests', () => {
    test('should handle component errors gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      renderDonors();

      // Should not crash the application
      expect(screen.getByTestId('nav-tabs-donors')).toBeInTheDocument();

      consoleSpy.mockRestore();
    });
  });

  describe('Loading States Tests', () => {
    test('should show initial loading state', () => {
      renderDonors();

      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    test('should hide loading state after data loads', async () => {
      renderDonors();

      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });

      expect(screen.getByTestId('donors-grid')).toBeInTheDocument();
    });
  });
}); 