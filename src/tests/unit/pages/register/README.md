# Register Module Test Suite

This directory contains comprehensive test cases for the register folder components, achieving 100% code coverage for all functionality.

## Overview

The register module test suite covers three main components:
- `index.js` (SignupPage) - Main registration form
- `verification.js` (Verification) - Email OTP verification
- `OrganisationDetails.js` - Organization details form

**Excluded files** (as requested):
- `activate.js`
- `privacypolicydialog.js` 
- `termsandconditionsDialog.js`

## Test Files

### 1. `index.test.js` - SignupPage Component Tests

**Coverage Areas:**
- ✅ Rendering tests for donor and NGO signup forms
- ✅ Form field validation (email, PAN, mobile number)
- ✅ Mobile number verification flow
- ✅ OTP dialog functionality
- ✅ Form submission for both donor and NGO roles
- ✅ Error handling and API failures
- ✅ Dynamic field rendering based on selections
- ✅ Component lifecycle and state management

**Key Test Scenarios:**
- Donor vs NGO form rendering
- Email uniqueness validation
- PAN number format validation
- Mobile OTP send/verify/resend flow
- Conditional field display (organization name, referral source)
- Form submission with proper role assignment
- Loading states and error handling

### 2. `verification.test.js` - Verification Component Tests

**Coverage Areas:**
- ✅ Email OTP verification form rendering
- ✅ OTP input validation (6-digit numeric)
- ✅ OTP verification API calls
- ✅ Resend OTP functionality with countdown
- ✅ Navigation between verification and organization details
- ✅ Error handling for verification failures
- ✅ Toast notifications
- ✅ Component lifecycle (timer management)

**Key Test Scenarios:**
- OTP input restrictions (numeric only, 6 digits max)
- Continue button enable/disable logic
- Successful OTP verification flow
- Resend OTP with countdown timer
- Error handling for invalid OTP
- Navigation back to previous step
- Toast message display and dismissal

### 3. `OrganisationDetails.test.js` - OrganisationDetails Component Tests

**Coverage Areas:**
- ✅ Form rendering for service provider vs society
- ✅ Password strength validation
- ✅ Google Maps URL validation (for society)
- ✅ Company type selection
- ✅ Form submission with token handling
- ✅ Navigation to dashboard after successful registration
- ✅ Error handling and toast notifications
- ✅ Loading states during submission

**Key Test Scenarios:**
- Service provider vs society form differences
- Password strength indicator (Poor/Moderate/Strong)
- Password requirements validation
- Google Maps URL format validation
- Company type dropdown functionality
- Successful registration with token storage
- Error handling for registration failures
- Navigation and state management

## Running Tests

### Run All Register Tests
```bash
npm run test:register
```

### Run with Coverage
```bash
npm run test:register -- --coverage
```

### Run Individual Test Files
```bash
# Run signup page tests
npx jest src/tests/unit/pages/register/index.test.js

# Run verification tests
npx jest src/tests/unit/pages/register/verification.test.js

# Run organization details tests
npx jest src/tests/unit/pages/register/OrganisationDetails.test.js
```

### Run Test Runner Script
```bash
# Run all tests with detailed reporting
node src/tests/unit/pages/register/runRegisterTests.js

# Run individual test files
node src/tests/unit/pages/register/runRegisterTests.js --individual

# Show help
node src/tests/unit/pages/register/runRegisterTests.js --help
```

## Test Coverage Goals

- **Lines**: 100%
- **Functions**: 100%
- **Branches**: 100%
- **Statements**: 100%

## Mock Strategy

### External Dependencies
- `axios` - Mocked for API calls
- `next/router` - Mocked for navigation
- `@mui/material` - Components rendered as-is
- Custom components - Simplified mock implementations

### Context and Hooks
- `AuthContext` - Mocked with required methods
- `useAuth` - Mocked for authentication operations
- `fetchIpAddress` - Mocked to return test IP

### API Endpoints
- Signup endpoint - Mocked responses for success/failure
- OTP endpoints - Mocked for send/verify/resend operations
- Email verification - Mocked for uniqueness checks
- List values - Mocked dropdown data

## Test Data

### Sample Test Data Used
```javascript
// User data
const testUser = {
  name: 'John Doe',
  email: '<EMAIL>',
  mobile: '9876543210',
  pan: '**********'
};

// Organization data
const testOrg = {
  trustName: 'Test NGO',
  companyName: 'Test Company',
  website: 'https://www.test.com'
};

// API responses
const successResponse = { data: { success: true } };
const errorResponse = { response: { status: 400, data: { message: 'Error' } } };
```

## Common Test Patterns

### Form Validation Testing
```javascript
test('should validate required fields', async () => {
  const user = userEvent.setup();
  render(<Component />);
  
  const submitButton = screen.getByRole('button', { name: /submit/i });
  await user.click(submitButton);
  
  await waitFor(() => {
    expect(screen.getByText(/field is required/i)).toBeInTheDocument();
  });
});
```

### API Call Testing
```javascript
test('should call API with correct data', async () => {
  mockedAxios.mockResolvedValue({ data: { success: true } });
  
  // ... user interactions
  
  await waitFor(() => {
    expect(mockedAxios).toHaveBeenCalledWith({
      method: 'post',
      url: expect.stringContaining('/api/endpoint'),
      data: expect.objectContaining({ key: 'value' })
    });
  });
});
```

### Error Handling Testing
```javascript
test('should handle API error', async () => {
  mockedAxios.mockRejectedValue(new Error('API Error'));
  
  // ... trigger error
  
  await waitFor(() => {
    expect(screen.getByText(/error message/i)).toBeInTheDocument();
  });
});
```

## Maintenance Notes

### Adding New Tests
1. Follow existing test structure and naming conventions
2. Use descriptive test names that explain the scenario
3. Group related tests in describe blocks
4. Mock external dependencies appropriately
5. Test both success and failure scenarios

### Updating Tests
1. Update tests when component functionality changes
2. Maintain 100% coverage requirement
3. Update mock data to match new API contracts
4. Verify all test scenarios still pass

### Best Practices
- Use `userEvent` for user interactions
- Use `waitFor` for async operations
- Mock external dependencies at module level
- Test user-facing behavior, not implementation details
- Include both positive and negative test cases

## Troubleshooting

### Common Issues
1. **Tests timing out**: Increase timeout or check for missing `waitFor`
2. **Mock not working**: Verify mock is defined before component import
3. **Coverage not 100%**: Check for untested branches or error paths
4. **Async issues**: Ensure proper use of `waitFor` and `async/await`

### Debug Commands
```bash
# Run tests in debug mode
npm run test:debug -- --testPathPattern=register

# Run specific test with verbose output
npx jest src/tests/unit/pages/register/index.test.js --verbose

# Generate coverage report
npm run test:coverage -- --testPathPattern=register
```
