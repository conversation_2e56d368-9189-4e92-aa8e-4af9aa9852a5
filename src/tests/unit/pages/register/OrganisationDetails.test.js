import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useRouter } from 'next/router';
import axios from 'axios';
import OrganisationDetails from 'src/pages/register/OrganisationDetails.js';
import { AuthContext } from 'src/context/AuthContext';
import { fetchIpAddress } from 'src/@core/components/custom-components/FetchIpAddress';

// Mock dependencies
jest.mock('axios');
jest.mock('src/@core/components/custom-components/FetchIpAddress');
jest.mock('src/configs/auth', () => ({
  signUpEndpoint: '/api/signup',
  storageTokenKeyName: 'accessToken',
  refreshTokenKeyName: 'refreshToken',
  companyType: 'company-type-list',
}));

// Mock Icon component
jest.mock('src/@core/components/icon', () => {
  return function Icon({ icon, onClick }) {
    return <span data-testid="icon" onClick={onClick}>{icon}</span>;
  };
});

// Mock SelectAutoComplete
jest.mock('src/@core/components/custom-components/SelectAutoComplete', () => {
  return function SelectAutoComplete({ nameArray, value, onChange, label, register }) {
    return (
      <select
        data-testid={`select-${label?.toLowerCase().replace(/\s+/g, '-')}`}
        value={value || ''}
        onChange={onChange}
      >
        <option value="">Select {label}</option>
        {nameArray?.map((item) => (
          <option key={item.value} value={item.value}>
            {item.key}
          </option>
        ))}
      </select>
    );
  };
});

// Mock GoogleMapsIconButton
jest.mock('src/@core/components/custom-components/toastDisplay', () => {
  return function GoogleMapsIconButton() {
    return <button data-testid="google-maps-button">Maps</button>;
  };
});

// Mock FallbackSpinner
jest.mock('src/@core/components/spinner', () => {
  return function FallbackSpinner() {
    return <div data-testid="fallback-spinner">Loading...</div>;
  };
});

const mockedAxios = axios;

describe('OrganisationDetails', () => {
  const mockPush = jest.fn();
  const mockReplace = jest.fn();
  const mockReload = jest.fn();
  const mockSetShowPwd = jest.fn();
  const mockFetchProfile = jest.fn();
  const mockGetAllListValuesByListNameId = jest.fn();
  const mockSetPageLoad = jest.fn();

  const mockAuthContextValue = {
    fetchProfile: mockFetchProfile,
    getAllListValuesByListNameId: mockGetAllListValuesByListNameId,
    setPageLoad: mockSetPageLoad,
  };

  const defaultProps = {
    email: '<EMAIL>',
    setShowPwd: mockSetShowPwd,
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock useRouter
    const { useRouter } = require('next/router');
    useRouter.mockReturnValue({
      push: mockPush,
      replace: mockReplace,
      reload: mockReload,
      pathname: '/register/organisation-details',
      query: { role: 'service-provider' },
      asPath: '/register/organisation-details?role=service-provider',
      isReady: true,
      route: '/register/organisation-details',
    });

    fetchIpAddress.mockResolvedValue('***********');

    // Mock company type list values
    mockGetAllListValuesByListNameId.mockImplementation((listId, callback) => {
      if (listId === 'company-type-list') {
        callback({
          listValues: [
            { id: 'type1', listValue: 'Private Limited' },
            { id: 'type2', listValue: 'Public Limited' },
            { id: 'type3', listValue: 'Partnership' },
          ],
        });
      }
    });

    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        setItem: jest.fn(),
        getItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn(),
      },
      writable: true,
    });
  });

  const renderOrganisationDetails = (contextValue = mockAuthContextValue, routerQuery = { role: 'service-provider' }) => {
    const { useRouter } = require('next/router');
    useRouter.mockReturnValue({
      push: mockPush,
      replace: mockReplace,
      reload: mockReload,
      pathname: '/register/organisation-details',
      query: routerQuery,
      asPath: `/register/organisation-details?role=${routerQuery.role}`,
      isReady: true,
      route: '/register/organisation-details',
    });

    return render(
      <AuthContext.Provider value={contextValue}>
        <OrganisationDetails {...defaultProps} />
      </AuthContext.Provider>
    );
  };

  describe('Rendering Tests', () => {
    test('should render all form fields', () => {
      renderOrganisationDetails();
      
      expect(screen.getByLabelText(/first name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/last name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/company name/i)).toBeInTheDocument();
      expect(screen.getByTestId('select-registration-type*')).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /sign up/i })).toBeInTheDocument();
    });

    test('should render society fields when role is society', () => {
      renderOrganisationDetails(mockAuthContextValue, { role: 'society' });
      
      expect(screen.getByLabelText(/society name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/google location/i)).toBeInTheDocument();
      expect(screen.getByTestId('google-maps-button')).toBeInTheDocument();
      expect(screen.queryByTestId('select-registration-type*')).not.toBeInTheDocument();
    });

    test('should display email address', () => {
      renderOrganisationDetails();
      
      expect(screen.getByText('Email :')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });


  });

  describe('Form Field Tests', () => {
    test('should handle first name input', async () => {
      const user = userEvent.setup();
      renderOrganisationDetails();
      
      const firstNameInput = screen.getByLabelText(/first name/i);
      await user.type(firstNameInput, 'John');
      
      expect(firstNameInput).toHaveValue('John');
    });

    test('should handle last name input', async () => {
      const user = userEvent.setup();
      renderOrganisationDetails();
      
      const lastNameInput = screen.getByLabelText(/last name/i);
      await user.type(lastNameInput, 'Doe');
      
      expect(lastNameInput).toHaveValue('Doe');
    });

    test('should handle organisation name input', async () => {
      const user = userEvent.setup();
      renderOrganisationDetails();
      
      const orgNameInput = screen.getByLabelText(/company name/i);
      await user.type(orgNameInput, 'Test Company');
      
      expect(orgNameInput).toHaveValue('Test Company');
    });

    test('should handle company type selection', async () => {
      const user = userEvent.setup();
      renderOrganisationDetails();
      
      const companyTypeSelect = screen.getByTestId('select-registration-type*');
      await user.selectOptions(companyTypeSelect, 'type1');
      
      expect(companyTypeSelect).toHaveValue('type1');
    });

    test('should handle password input', async () => {
      const user = userEvent.setup();
      renderOrganisationDetails();
      
      const passwordInput = screen.getByLabelText(/password/i);
      await user.type(passwordInput, 'TestPassword123!');
      
      expect(passwordInput).toHaveValue('TestPassword123!');
    });


  });



  describe('Google Location Validation Tests', () => {
    test('should validate Google Maps URL for society', async () => {
      const user = userEvent.setup();
      renderOrganisationDetails(mockAuthContextValue, { role: 'society' });
      
      const locationInput = screen.getByLabelText(/google location/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });
      
      await user.type(locationInput, 'invalid-url');
      await user.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/please enter a valid google maps url/i)).toBeInTheDocument();
      });
    });

    test('should accept valid Google Maps URL', async () => {
      const user = userEvent.setup();
      renderOrganisationDetails(mockAuthContextValue, { role: 'society' });
      
      const locationInput = screen.getByLabelText(/google location/i);
      
      await user.type(locationInput, 'https://maps.app.goo.gl/example');
      
      // Should not show validation error
      expect(screen.queryByText(/please enter a valid google maps url/i)).not.toBeInTheDocument();
    });
  });

  describe('Form Submission Tests', () => {





  });



  describe('Component Lifecycle Tests', () => {
    test('should load company type options on mount', () => {
      renderOrganisationDetails();
      
      expect(mockGetAllListValuesByListNameId).toHaveBeenCalledWith(
        'company-type-list',
        expect.any(Function),
        expect.any(Function)
      );
    });

    test('should handle error when loading company types', () => {
      const mockGetAllListValuesError = jest.fn((listId, callback, errorCallback) => {
        errorCallback(new Error('API Error'));
      });
      
      const contextWithError = {
        ...mockAuthContextValue,
        getAllListValuesByListNameId: mockGetAllListValuesError,
      };
      
      renderOrganisationDetails(contextWithError);
      
      expect(mockGetAllListValuesError).toHaveBeenCalled();
    });
  });


});
