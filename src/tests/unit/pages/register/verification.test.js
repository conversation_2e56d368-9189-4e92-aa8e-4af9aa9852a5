import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import Verification from 'src/pages/register/verification.js';
import { useAuth } from 'src/hooks/useAuth';
import { fetchIpAddress } from 'src/@core/components/custom-components/FetchIpAddress';

// Mock dependencies
jest.mock('src/hooks/useAuth');
jest.mock('src/@core/components/custom-components/FetchIpAddress');
jest.mock('src/pages/register/OrganisationDetails', () => {
  return function OrganisationDetails({ email, setShowPwd }) {
    return (
      <div data-testid="organisation-details">
        <p>Organisation Details for {email}</p>
        <button onClick={() => setShowPwd(false)}>Back to Password</button>
      </div>
    );
  };
});

// Mock Icon component
jest.mock('src/@core/components/icon', () => {
  return function Icon({ icon, onClick }) {
    return <span data-testid="icon" onClick={onClick}>{icon}</span>;
  };
});

describe('Verification', () => {
  const mockVerifyOTP = jest.fn();
  const mockResendOTP = jest.fn();
  const mockSetShowPwd = jest.fn();
  const mockSetShowDetails = jest.fn();
  const mockSetCountdown = jest.fn();

  const mockAuth = {
    verifyOTP: mockVerifyOTP,
    resendOTP: mockResendOTP,
  };

  const defaultProps = {
    email: '<EMAIL>',
    setShowPwd: mockSetShowPwd,
    setShowDetails: mockSetShowDetails,
    showDetails: false,
    countdown: 30,
    setCountdown: mockSetCountdown,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useAuth.mockReturnValue(mockAuth);
    fetchIpAddress.mockResolvedValue('***********');
  });

  const renderVerification = (props = {}) => {
    return render(<Verification {...defaultProps} {...props} />);
  };

  describe('Rendering Tests', () => {
    test('should render verification form when showDetails is false', () => {
      renderVerification();
      
      expect(screen.getByText('Verify Your Email')).toBeInTheDocument();
      expect(screen.getByText(/please enter the 6-digit code/i)).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('OTP')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /continue/i })).toBeInTheDocument();
    });

    test('should render OrganisationDetails when showDetails is true', () => {
      renderVerification({ showDetails: true });
      
      expect(screen.getByTestId('organisation-details')).toBeInTheDocument();
      expect(screen.getByText('Organisation <NAME_EMAIL>')).toBeInTheDocument();
      expect(screen.queryByText('Verify Your Email')).not.toBeInTheDocument();
    });

    test('should render back arrow button', () => {
      renderVerification();
      
      expect(screen.getByTestId('icon')).toBeInTheDocument();
    });

    test('should display email address correctly', () => {
      renderVerification({ email: '<EMAIL>' });
      
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    test('should render resend OTP section', () => {
      renderVerification();
      
      expect(screen.getByText(/didn't get an email/i)).toBeInTheDocument();
    });
  });

  describe('OTP Input Tests', () => {
    test('should accept only numeric input', async () => {
      const user = userEvent.setup();
      renderVerification();
      
      const otpInput = screen.getByPlaceholderText('OTP');
      
      await user.type(otpInput, 'abc123def');
      
      expect(otpInput).toHaveValue('123');
    });

    test('should limit input to 6 digits', async () => {
      const user = userEvent.setup();
      renderVerification();
      
      const otpInput = screen.getByPlaceholderText('OTP');
      
      await user.type(otpInput, '1234567890');
      
      expect(otpInput).toHaveValue('123456');
    });

    test('should enable continue button when OTP is 6 digits', async () => {
      const user = userEvent.setup();
      renderVerification();
      
      const otpInput = screen.getByPlaceholderText('OTP');
      const continueButton = screen.getByRole('button', { name: /continue/i });
      
      expect(continueButton).toBeDisabled();
      
      await user.type(otpInput, '123456');
      
      expect(continueButton).not.toBeDisabled();
    });

    test('should disable continue button when OTP is less than 6 digits', async () => {
      const user = userEvent.setup();
      renderVerification();
      
      const otpInput = screen.getByPlaceholderText('OTP');
      const continueButton = screen.getByRole('button', { name: /continue/i });
      
      await user.type(otpInput, '12345');
      
      expect(continueButton).toBeDisabled();
    });
  });



  describe('OTP Verification Tests', () => {
    test('should call verifyOTP with correct data when continue is clicked', async () => {
      const user = userEvent.setup();
      mockVerifyOTP.mockResolvedValue(true);
      
      renderVerification();
      
      const otpInput = screen.getByPlaceholderText('OTP');
      await user.type(otpInput, '123456');
      
      const continueButton = screen.getByRole('button', { name: /continue/i });
      await user.click(continueButton);
      
      await waitFor(() => {
        expect(mockVerifyOTP).toHaveBeenCalledWith(
          {
            contactType: 'EMAIL',
            contactValue: '<EMAIL>',
            otp: '123456',
          },
          expect.any(Function)
        );
      });
    });

    test('should show details when OTP verification is successful', async () => {
      const user = userEvent.setup();
      mockVerifyOTP.mockResolvedValue(true);
      
      renderVerification();
      
      const otpInput = screen.getByPlaceholderText('OTP');
      await user.type(otpInput, '123456');
      
      const continueButton = screen.getByRole('button', { name: /continue/i });
      await user.click(continueButton);
      
      await waitFor(() => {
        expect(mockSetShowDetails).toHaveBeenCalledWith(true);
      });
    });


  });

  describe('Resend OTP Tests', () => {
    test('should show countdown when countdown is greater than 0', () => {
      renderVerification({ countdown: 25 });
      
      expect(screen.getByText(/resend otp in: 25s/i)).toBeInTheDocument();
    });

    test('should show resend link when countdown is 0', () => {
      renderVerification({ countdown: 0 });
      
      expect(screen.getByText('Resend OTP')).toBeInTheDocument();
      expect(screen.queryByText(/resend otp in:/i)).not.toBeInTheDocument();
    });

    test('should call resendOTP when resend link is clicked', async () => {
      const user = userEvent.setup();
      mockResendOTP.mockResolvedValue(true);
      
      renderVerification({ countdown: 0 });
      
      const resendLink = screen.getByText('Resend OTP');
      await user.click(resendLink);
      
      await waitFor(() => {
        expect(mockResendOTP).toHaveBeenCalledWith(
          {
            contactType: 'EMAIL',
            contactValue: '<EMAIL>',
            ipAddress: '***********',
          },
          expect.any(Function)
        );
      });
    });

    test('should reset countdown after successful resend', async () => {
      const user = userEvent.setup();
      mockResendOTP.mockResolvedValue(true);
      
      renderVerification({ countdown: 0 });
      
      const resendLink = screen.getByText('Resend OTP');
      await user.click(resendLink);
      
      await waitFor(() => {
        expect(mockSetCountdown).toHaveBeenCalledWith(3);
      });
    });


  });

  describe('Navigation Tests', () => {
    test('should call setShowPwd when back arrow is clicked', async () => {
      const user = userEvent.setup();
      renderVerification();
      
      const backButton = screen.getByTestId('icon');
      await user.click(backButton);
      
      expect(mockSetShowPwd).toHaveBeenCalledWith(false);
    });
  });



  describe('Component Lifecycle Tests', () => {
    test('should handle countdown timer', () => {
      jest.useFakeTimers();
      
      renderVerification({ countdown: 5 });
      
      // Fast-forward time
      jest.advanceTimersByTime(1000);
      
      expect(mockSetCountdown).toHaveBeenCalledWith(4);
      
      jest.useRealTimers();
    });

    test('should cleanup timer on unmount', () => {
      jest.useFakeTimers();
      
      const { unmount } = renderVerification({ countdown: 5 });
      
      unmount();
      
      // Fast-forward time after unmount
      jest.advanceTimersByTime(1000);
      
      // Should not call setCountdown after unmount
      expect(mockSetCountdown).not.toHaveBeenCalled();
      
      jest.useRealTimers();
    });
  });
});
