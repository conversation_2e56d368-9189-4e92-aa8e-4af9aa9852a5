#!/usr/bin/env node

/**
 * Test Runner for Register Module
 * 
 * This script runs all tests for the register folder components
 * and provides detailed coverage reporting.
 */

const { execSync } = require('child_process');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function runCommand(command, description) {
  log(`\n${colors.cyan}${colors.bright}Running: ${description}${colors.reset}`);
  log(`${colors.yellow}Command: ${command}${colors.reset}`);
  
  try {
    const output = execSync(command, { 
      stdio: 'inherit',
      cwd: process.cwd(),
      encoding: 'utf8'
    });
    log(`${colors.green}✓ ${description} completed successfully${colors.reset}`);
    return true;
  } catch (error) {
    log(`${colors.red}✗ ${description} failed${colors.reset}`);
    log(`${colors.red}Error: ${error.message}${colors.reset}`);
    return false;
  }
}

function main() {
  log(`${colors.bright}${colors.magenta}=== Register Module Test Suite ===${colors.reset}`);
  log(`${colors.cyan}Testing components: index.js, verification.js, OrganisationDetails.js${colors.reset}`);
  log(`${colors.yellow}Excluded: activate.js, privacypolicydialog.js, termsandconditionsDialog.js${colors.reset}`);
  
  const testCommands = [
    {
      command: 'npm run test:register -- --verbose --coverage',
      description: 'Running all register tests with coverage'
    },
    {
      command: 'npm run test:register -- --testNamePattern="Rendering Tests" --verbose',
      description: 'Running rendering tests'
    },
    {
      command: 'npm run test:register -- --testNamePattern="Form Validation Tests" --verbose',
      description: 'Running form validation tests'
    },
    {
      command: 'npm run test:register -- --testNamePattern="OTP" --verbose',
      description: 'Running OTP-related tests'
    },
    {
      command: 'npm run test:register -- --testNamePattern="Submission Tests" --verbose',
      description: 'Running form submission tests'
    },
    {
      command: 'npm run test:register -- --testNamePattern="Error Handling" --verbose',
      description: 'Running error handling tests'
    }
  ];

  let passedTests = 0;
  let totalTests = testCommands.length;

  for (const { command, description } of testCommands) {
    if (runCommand(command, description)) {
      passedTests++;
    }
  }

  // Summary
  log(`\n${colors.bright}${colors.magenta}=== Test Summary ===${colors.reset}`);
  log(`${colors.green}Passed: ${passedTests}/${totalTests}${colors.reset}`);
  
  if (passedTests === totalTests) {
    log(`${colors.green}${colors.bright}🎉 All register tests passed!${colors.reset}`);
    
    // Run coverage report
    log(`\n${colors.cyan}Generating detailed coverage report...${colors.reset}`);
    runCommand(
      'npm run test:register -- --coverage --coverageReporters=text-lcov --coverageReporters=html',
      'Generating coverage report'
    );
    
    log(`${colors.green}Coverage report generated in coverage/lcov-report/index.html${colors.reset}`);
  } else {
    log(`${colors.red}${colors.bright}❌ Some tests failed. Please check the output above.${colors.reset}`);
    process.exit(1);
  }
}

// Run individual test files
function runIndividualTests() {
  log(`\n${colors.bright}${colors.blue}=== Running Individual Test Files ===${colors.reset}`);
  
  const testFiles = [
    'src/tests/unit/pages/register/index.test.js',
    'src/tests/unit/pages/register/verification.test.js',
    'src/tests/unit/pages/register/OrganisationDetails.test.js'
  ];

  for (const testFile of testFiles) {
    const fileName = path.basename(testFile);
    runCommand(
      `npx jest ${testFile} --verbose --coverage`,
      `Running ${fileName}`
    );
  }
}

// Check if specific test file is requested
const args = process.argv.slice(2);
if (args.includes('--individual')) {
  runIndividualTests();
} else if (args.includes('--help')) {
  log(`${colors.bright}Register Test Runner${colors.reset}`);
  log(`${colors.cyan}Usage:${colors.reset}`);
  log(`  node runRegisterTests.js           - Run all register tests`);
  log(`  node runRegisterTests.js --individual - Run individual test files`);
  log(`  node runRegisterTests.js --help    - Show this help message`);
} else {
  main();
}
