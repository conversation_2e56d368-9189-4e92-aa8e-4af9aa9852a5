import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import axios from 'axios';
import DeleteDialog from 'src/pages/donation-head/DeleteDialog';
import { AuthContext } from 'src/context/AuthContext';

// Mock axios
jest.mock('axios');
const mockedAxios = axios;
mockedAxios.delete = jest.fn();
mockedAxios.patch = jest.fn();

// Mock the auth config
jest.mock('src/configs/auth', () => ({
  baseURL: 'http://localhost:8080/pheart/',
  donationHeadEndpoint: 'donation-heads'
}));

// Mock utils
jest.mock('src/helpers/utils', () => ({
  getAuthorizationHeaders: jest.fn(() => ({
    'Authorization': 'Bearer test-token',
    'Content-Type': 'application/json'
  }))
}));

describe('DeleteDialog Component', () => {
  const mockOnClose = jest.fn();
  
  const defaultProps = {
    open: true,
    onClose: mockOnClose,
    data: {
      id: 1,
      name: 'Test Donation Head',
      isActive: true
    }
  };

  const mockAuthContext = {
    user: {
      id: 1,
      name: 'Test User',
      organisationCategory: 'TENANT'
    },
    logout: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockedAxios.delete.mockResolvedValue({ data: { success: true } });
    mockedAxios.patch.mockResolvedValue({ data: { success: true } });
  });

  const renderComponent = (props = {}) => {
    return render(
      <AuthContext.Provider value={mockAuthContext}>
        <DeleteDialog {...defaultProps} {...props} />
      </AuthContext.Provider>
    );
  };

  test('should render dialog when open', () => {
    renderComponent();
    
    // Check if dialog content is present
    expect(screen.getByText(/Are you sure you want to deactivate/)).toBeInTheDocument();
    expect(screen.getByText(/Test Donation Head/)).toBeInTheDocument();
  });

  test('should not render dialog when closed', () => {
    renderComponent({ open: false });
    
    // Dialog should not be visible
    expect(screen.queryByText(/Are you sure you want to deactivate/)).not.toBeInTheDocument();
  });

  test('should display deactivate message for active item', () => {
    renderComponent({ data: { id: 1, name: 'Active Item', isActive: true } });
    
    expect(screen.getByText(/Are you sure you want to deactivate/)).toBeInTheDocument();
    expect(screen.getByText(/Active Item/)).toBeInTheDocument();
    expect(screen.getByText('DeActivate')).toBeInTheDocument();
  });

  test('should display activate message for inactive item', () => {
    renderComponent({ data: { id: 1, name: 'Inactive Item', isActive: false } });
    
    expect(screen.getByText(/Are you sure, you want to activate/)).toBeInTheDocument();
    expect(screen.getByText('Activate')).toBeInTheDocument();
  });

  test('should handle cancel button click', () => {
    renderComponent();
    
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);
    
    expect(mockOnClose).toHaveBeenCalled();
  });

  test('should handle deactivate button click', async () => {
    renderComponent();
    
    const deactivateButton = screen.getByText('DeActivate');
    fireEvent.click(deactivateButton);
    
    await waitFor(() => {
      expect(mockedAxios.delete).toHaveBeenCalledWith(
        'http://localhost:8080/pheart/donation-heads/1',
        {
          headers: {
            'Authorization': 'Bearer test-token',
            'Content-Type': 'application/json'
          }
        }
      );
    });
  });

  test('should handle activate button click', async () => {
    renderComponent({ data: { id: 1, name: 'Test Item', isActive: false } });
    
    const activateButton = screen.getByText('Activate');
    fireEvent.click(activateButton);
    
    await waitFor(() => {
      expect(mockedAxios.patch).toHaveBeenCalledWith(
        'http://localhost:8080/pheart/donation-heads/1',
        {
          headers: {
            'Authorization': 'Bearer test-token',
            'Content-Type': 'application/json'
          }
        }
      );
    });
  });

  test('should show loading state during operation', async () => {
    // Mock a delayed response
    mockedAxios.delete.mockImplementation(() => new Promise(resolve => setTimeout(() => resolve({ data: { success: true } }), 100)));
    
    renderComponent();
    
    const deactivateButton = screen.getByText('DeActivate');
    fireEvent.click(deactivateButton);
    
    // Should show loading spinner
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });
  });

  test('should handle API error gracefully', async () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    mockedAxios.delete.mockRejectedValue(new Error('API Error'));
    
    renderComponent();
    
    const deactivateButton = screen.getByText('DeActivate');
    fireEvent.click(deactivateButton);
    
    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith('Delete operation failed', expect.any(Error));
    });
    
    consoleErrorSpy.mockRestore();
  });

  test('should handle missing data gracefully', () => {
    renderComponent({ data: null });
    
    // Should still render without crashing
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  test('should handle data without name', () => {
    renderComponent({ data: { id: 1, isActive: true } });
    
    // Should still render without crashing
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  test('should handle undefined isActive status', () => {
    renderComponent({ data: { id: 1, name: 'Test Item' } });
    
    // Should default to activate behavior when isActive is undefined
    expect(screen.getByText(/Are you sure, you want to activate/)).toBeInTheDocument();
  });

  test('should close dialog after successful operation', async () => {
    renderComponent();
    
    const deactivateButton = screen.getByText('DeActivate');
    fireEvent.click(deactivateButton);
    
    await waitFor(() => {
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  test('should show success message after operation', async () => {
    mockedAxios.delete.mockResolvedValue({ data: { success: true } });
    
    renderComponent();
    
    const deactivateButton = screen.getByText('DeActivate');
    fireEvent.click(deactivateButton);
    
    await waitFor(() => {
      expect(screen.getByText('Test Donation Head Deactivated successfully.')).toBeInTheDocument();
    });
  });

  test('should handle component unmount', () => {
    const { unmount } = renderComponent();
    
    expect(() => unmount()).not.toThrow();
  });
});
