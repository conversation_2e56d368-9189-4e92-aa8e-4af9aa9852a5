/**
 * @jest-environment jsdom
 */

import { useAuth } from 'src/hooks/useAuth';

// Mock dependencies
jest.mock('src/hooks/useAuth');

const mockedUseAuth = useAuth;

describe('Donation Head Form Logic Tests', () => {
  let mockAuth;

  beforeEach(() => {
    jest.clearAllMocks();
    mockAuth = {
      postDonationHead: jest.fn(),
      patchDonationHead: jest.fn(),
    };
    mockedUseAuth.mockReturnValue(mockAuth);
  });

  describe('Form Submission Logic', () => {
    describe('submit function (Create)', () => {
      const mockSubmit = async (data, user, tenantId, handleSuccess, handleFailure) => {
        const fields = {
          name: data.donationHead,
          orgId: user?.organisationCategory === "SUPER_ADMIN" ? tenantId : user?.orgId,
          description: data.description,
        };

        try {
          const response = await mockAuth.postDonationHead(
            fields,
            handleFailure,
            handleSuccess
          );
          return response;
        } catch (error) {
          console.error("Donation Head creation failed:", error);
          handleFailure();
          throw error;
        }
      };

      test('should create donation head with correct data for regular user', async () => {
        const mockData = {
          donationHead: 'Education Fund',
          description: 'Support for education'
        };
        const mockUser = {
          organisationCategory: 'NGO',
          orgId: 'org-123'
        };
        const handleSuccess = jest.fn();
        const handleFailure = jest.fn();

        mockAuth.postDonationHead.mockResolvedValue({ id: 1 });

        await mockSubmit(mockData, mockUser, '', handleSuccess, handleFailure);

        expect(mockAuth.postDonationHead).toHaveBeenCalledWith(
          {
            name: 'Education Fund',
            orgId: 'org-123',
            description: 'Support for education'
          },
          handleFailure,
          handleSuccess
        );
      });

      test('should create donation head with tenant ID for super admin', async () => {
        const mockData = {
          donationHead: 'Healthcare Fund',
          description: 'Medical support'
        };
        const mockUser = {
          organisationCategory: 'SUPER_ADMIN',
          orgId: 'admin-org'
        };
        const tenantId = 'selected-tenant-123';
        const handleSuccess = jest.fn();
        const handleFailure = jest.fn();

        mockAuth.postDonationHead.mockResolvedValue({ id: 2 });

        await mockSubmit(mockData, mockUser, tenantId, handleSuccess, handleFailure);

        expect(mockAuth.postDonationHead).toHaveBeenCalledWith(
          {
            name: 'Healthcare Fund',
            orgId: 'selected-tenant-123',
            description: 'Medical support'
          },
          handleFailure,
          handleSuccess
        );
      });

      test('should handle creation errors', async () => {
        const mockData = {
          donationHead: 'Test Fund',
          description: 'Test description'
        };
        const mockUser = { organisationCategory: 'NGO', orgId: 'org-123' };
        const handleSuccess = jest.fn();
        const handleFailure = jest.fn();
        const mockError = new Error('Creation failed');

        mockAuth.postDonationHead.mockRejectedValue(mockError);

        await expect(
          mockSubmit(mockData, mockUser, '', handleSuccess, handleFailure)
        ).rejects.toThrow('Creation failed');

        expect(handleFailure).toHaveBeenCalled();
      });

      test('should handle missing donation head name', async () => {
        const mockData = {
          donationHead: '',
          description: 'Test description'
        };
        const mockUser = { organisationCategory: 'NGO', orgId: 'org-123' };
        const handleSuccess = jest.fn();
        const handleFailure = jest.fn();

        await mockSubmit(mockData, mockUser, '', handleSuccess, handleFailure);

        expect(mockAuth.postDonationHead).toHaveBeenCalledWith(
          {
            name: '',
            orgId: 'org-123',
            description: 'Test description'
          },
          handleFailure,
          handleSuccess
        );
      });
    });

    describe('update function (Edit)', () => {
      const mockUpdate = async (data, user, tenantId, formData, handleSuccessUpdate, handleFailureUpdate) => {
        const fields = {
          name: data.donationHead,
          orgId: user?.organisationCategory === "SUPER_ADMIN" ? tenantId : user?.orgId,
          description: data.description,
        };

        try {
          const response = await mockAuth.patchDonationHead(
            formData?.id,
            fields,
            handleFailureUpdate,
            handleSuccessUpdate
          );
          return response;
        } catch (error) {
          console.error("Donation Head update failed:", error);
          handleFailureUpdate();
          throw error;
        }
      };

      test('should update donation head with correct data', async () => {
        const mockData = {
          donationHead: 'Updated Fund Name',
          description: 'Updated description'
        };
        const mockUser = {
          organisationCategory: 'NGO',
          orgId: 'org-123'
        };
        const mockFormData = { id: 1 };
        const handleSuccessUpdate = jest.fn();
        const handleFailureUpdate = jest.fn();

        mockAuth.patchDonationHead.mockResolvedValue({ id: 1 });

        await mockUpdate(mockData, mockUser, '', mockFormData, handleSuccessUpdate, handleFailureUpdate);

        expect(mockAuth.patchDonationHead).toHaveBeenCalledWith(
          1,
          {
            name: 'Updated Fund Name',
            orgId: 'org-123',
            description: 'Updated description'
          },
          handleFailureUpdate,
          handleSuccessUpdate
        );
      });

      test('should update with tenant ID for super admin', async () => {
        const mockData = {
          donationHead: 'Admin Updated Fund',
          description: 'Admin updated description'
        };
        const mockUser = {
          organisationCategory: 'SUPER_ADMIN',
          orgId: 'admin-org'
        };
        const tenantId = 'selected-tenant-456';
        const mockFormData = { id: 2 };
        const handleSuccessUpdate = jest.fn();
        const handleFailureUpdate = jest.fn();

        mockAuth.patchDonationHead.mockResolvedValue({ id: 2 });

        await mockUpdate(mockData, mockUser, tenantId, mockFormData, handleSuccessUpdate, handleFailureUpdate);

        expect(mockAuth.patchDonationHead).toHaveBeenCalledWith(
          2,
          {
            name: 'Admin Updated Fund',
            orgId: 'selected-tenant-456',
            description: 'Admin updated description'
          },
          handleFailureUpdate,
          handleSuccessUpdate
        );
      });

      test('should handle update errors', async () => {
        const mockData = {
          donationHead: 'Test Fund',
          description: 'Test description'
        };
        const mockUser = { organisationCategory: 'NGO', orgId: 'org-123' };
        const mockFormData = { id: 1 };
        const handleSuccessUpdate = jest.fn();
        const handleFailureUpdate = jest.fn();
        const mockError = new Error('Update failed');

        mockAuth.patchDonationHead.mockRejectedValue(mockError);

        await expect(
          mockUpdate(mockData, mockUser, '', mockFormData, handleSuccessUpdate, handleFailureUpdate)
        ).rejects.toThrow('Update failed');

        expect(handleFailureUpdate).toHaveBeenCalled();
      });
    });
  });

  describe('Form Data Processing Logic', () => {
    describe('Organization ID determination', () => {
      const determineOrgId = (user, tenantId) => {
        return user?.organisationCategory === "SUPER_ADMIN" ? tenantId : user?.orgId;
      };

      test('should use user orgId for non-super admin', () => {
        const user = { organisationCategory: 'NGO', orgId: 'ngo-123' };
        const tenantId = 'tenant-456';

        const result = determineOrgId(user, tenantId);

        expect(result).toBe('ngo-123');
      });

      test('should use tenantId for super admin', () => {
        const user = { organisationCategory: 'SUPER_ADMIN', orgId: 'admin-org' };
        const tenantId = 'tenant-789';

        const result = determineOrgId(user, tenantId);

        expect(result).toBe('tenant-789');
      });

      test('should handle undefined user', () => {
        const user = undefined;
        const tenantId = 'tenant-123';

        const result = determineOrgId(user, tenantId);

        expect(result).toBe(undefined);
      });

      test('should handle empty tenantId for super admin', () => {
        const user = { organisationCategory: 'SUPER_ADMIN', orgId: 'admin-org' };
        const tenantId = '';

        const result = determineOrgId(user, tenantId);

        expect(result).toBe('');
      });
    });
  });
});
