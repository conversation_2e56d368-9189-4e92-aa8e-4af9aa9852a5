/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import AdvancedSearch from 'src/pages/donation-head/AdvancedSearch';
import { AuthContext } from 'src/context/AuthContext';

// Mock dependencies
jest.mock('react-perfect-scrollbar', () => {
  return function PerfectScrollbar({ children }) {
    return <div data-testid="perfect-scrollbar">{children}</div>;
  };
});

// Mock react-hook-form
const mockSetValue = jest.fn();
const mockReset = jest.fn();
const mockHandleSubmit = jest.fn();
const mockControl = {};

jest.mock('react-hook-form', () => ({
  useForm: () => ({
    setValue: mockSetValue,
    control: mockControl,
    reset: mockReset,
    handleSubmit: mockHandleSubmit,
    formState: { errors: {} },
  }),
  Controller: ({ render, name }) => {
    return render({ 
      field: { 
        value: '', 
        onChange: jest.fn(), 
        onBlur: jest.fn(), 
        name 
      }, 
      fieldState: { error: null } 
    });
  },
}));

// Mock Material-UI components
jest.mock('@mui/material', () => ({
  FormControl: ({ children }) => <div data-testid="form-control">{children}</div>,
  Grid: ({ children, item, xs, sm, container, ...props }) => (
    <div data-testid={container ? "grid-container" : "grid-item"} {...props}>{children}</div>
  ),
  TextField: ({ label, placeholder, onChange, ...props }) => (
    <input 
      data-testid={`textfield-${label?.toLowerCase().replace(/\s+/g, '-')}`}
      placeholder={placeholder}
      onChange={onChange}
      {...props}
    />
  ),
}));

jest.mock('@mui/material/Box', () => {
  return function Box({ children, ...props }) {
    return <div data-testid="box" {...props}>{children}</div>;
  };
});

jest.mock('@mui/material/Button', () => {
  return function Button({ children, onClick, variant, ...props }) {
    return (
      <button 
        data-testid={`button-${variant || 'default'}`}
        onClick={onClick}
        {...props}
      >
        {children}
      </button>
    );
  };
});

jest.mock('@mui/material/Drawer', () => {
  return function Drawer({ children, open, onClose, ...props }) {
    return open ? (
      <div data-testid="drawer" onClick={onClose} {...props}>
        {children}
      </div>
    ) : null;
  };
});

jest.mock('@mui/material/IconButton', () => {
  return function IconButton({ children, onClick, ...props }) {
    return (
      <button data-testid="icon-button" onClick={onClick} {...props}>
        {children}
      </button>
    );
  };
});

jest.mock('@mui/material/Tooltip', () => {
  return function Tooltip({ children, title }) {
    return <div data-testid="tooltip" title={title}>{children}</div>;
  };
});

jest.mock('@mui/material/Typography', () => {
  return function Typography({ children, variant, ...props }) {
    return <span data-testid={`typography-${variant}`} {...props}>{children}</span>;
  };
});

// Mock styled components
jest.mock('@mui/material/styles', () => ({
  styled: (component) => (styles) => component,
}));

// Mock custom components
jest.mock('src/@core/components/custom-components/NameTextField', () => {
  return function NameTextField({ label, onChange, value, ...props }) {
    return (
      <input 
        data-testid={`name-textfield-${label?.toLowerCase().replace(/\s+/g, '-')}`}
        value={value || ''}
        onChange={onChange}
        {...props}
      />
    );
  };
});

jest.mock('src/@core/components/custom-components/SelectAutoComplete', () => {
  return function SelectAutoComplete({ label, nameArray = [], value, onChange, ...props }) {
    return (
      <select 
        data-testid={`select-${label?.toLowerCase().replace(/\s+/g, '-')}`}
        value={value || ''}
        onChange={(e) => onChange && onChange({ target: { value: e.target.value } })}
        {...props}
      >
        <option value="">Select...</option>
        {nameArray.map((item, index) => (
          <option key={index} value={item.id || item.value}>
            {item.name || item.label || item.key}
          </option>
        ))}
      </select>
    );
  };
});

jest.mock('src/@core/components/icon', () => {
  return function Icon({ icon }) {
    return <span data-testid="icon">{icon}</span>;
  };
});

jest.mock('src/@core/components/mui/avatar', () => {
  return function CustomAvatar({ children, onClick, ...props }) {
    return (
      <div data-testid="custom-avatar" onClick={onClick} {...props}>
        {children}
      </div>
    );
  };
});

describe('AdvancedSearch Component', () => {
  const mockToggle = jest.fn();
  const mockSetSearchingState = jest.fn();
  const mockClearAllFilters = jest.fn();
  const mockOnApplyFilters = jest.fn();

  const defaultProps = {
    open: true,
    toggle: mockToggle,
    selectedFilters: [],
    setSearchingState: mockSetSearchingState,
    clearAllFilters: mockClearAllFilters,
    onApplyFilters: mockOnApplyFilters,
    tenantsList: [
      { id: 'org-1', name: 'Test Org 1' },
      { id: 'org-2', name: 'Test Org 2' }
    ],
  };

  const mockAuthContext = {
    user: { organisationCategory: 'TENANT', orgId: 'org-123' },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup form submission
    mockHandleSubmit.mockImplementation((callback) => (event) => {
      event?.preventDefault();
      callback({
        donationHead: 'Test Fund',
        description: 'Test Description'
      });
    });
  });

  const renderWithContext = (props = {}, contextOverrides = {}) => {
    const authContextValue = { ...mockAuthContext, ...contextOverrides };
    const componentProps = { ...defaultProps, ...props };

    return render(
      <AuthContext.Provider value={authContextValue}>
        <AdvancedSearch {...componentProps} />
      </AuthContext.Provider>
    );
  };

  // Basic rendering tests
  test('should render tooltip and avatar when closed', () => {
    renderWithContext({ open: false });
    
    expect(screen.getByTestId('tooltip')).toBeInTheDocument();
    expect(screen.getByTestId('custom-avatar')).toBeInTheDocument();
    expect(screen.queryByTestId('drawer')).not.toBeInTheDocument();
  });

  test('should render drawer when open', () => {
    renderWithContext();
    
    expect(screen.getByTestId('drawer')).toBeInTheDocument();
    expect(screen.getByTestId('typography-h5')).toBeInTheDocument();
    expect(screen.getByText('Advanced Search')).toBeInTheDocument();
  });

  test('should render form fields', () => {
    renderWithContext();
    
    expect(screen.getByTestId('name-textfield-donation-head')).toBeInTheDocument();
    expect(screen.getByTestId('textfield-description')).toBeInTheDocument();
  });

  test('should render NGO selection for super admin', () => {
    renderWithContext({}, { user: { organisationCategory: 'SUPER_ADMIN' } });
    
    expect(screen.getByTestId('select-ngo-name')).toBeInTheDocument();
  });

  test('should not render NGO selection for regular users', () => {
    renderWithContext();
    
    expect(screen.queryByTestId('select-ngo-name')).not.toBeInTheDocument();
  });

  // Interaction tests
  test('should handle avatar click to toggle drawer', () => {
    renderWithContext({ open: false });
    
    const avatar = screen.getByTestId('custom-avatar');
    fireEvent.click(avatar);
    
    expect(mockToggle).toHaveBeenCalled();
  });

  test('should handle close button click', () => {
    renderWithContext();
    
    const closeButton = screen.getByTestId('icon-button');
    fireEvent.click(closeButton);
    
    expect(mockToggle).toHaveBeenCalled();
  });

  test('should handle drawer backdrop click', () => {
    renderWithContext();
    
    const drawer = screen.getByTestId('drawer');
    fireEvent.click(drawer);
    
    expect(mockToggle).toHaveBeenCalled();
  });

  test('should handle clear all button', () => {
    renderWithContext();
    
    const clearButton = screen.getByTestId('button-tonal');
    fireEvent.click(clearButton);
    
    expect(mockReset).toHaveBeenCalled();
    expect(mockSetSearchingState).toHaveBeenCalledWith(false);
    expect(mockClearAllFilters).toHaveBeenCalled();
  });

  test('should handle apply button', () => {
    renderWithContext();
    
    const applyButton = screen.getByTestId('button-contained');
    fireEvent.click(applyButton);
    
    expect(mockHandleSubmit).toHaveBeenCalled();
  });

  // Form field interactions
  test('should handle donation head input change', () => {
    renderWithContext();
    
    const donationHeadInput = screen.getByTestId('name-textfield-donation-head');
    fireEvent.change(donationHeadInput, { target: { value: 'Education Fund' } });
    
    expect(donationHeadInput).toBeInTheDocument();
  });

  test('should handle description input change', () => {
    renderWithContext();
    
    const descriptionInput = screen.getByTestId('textfield-description');
    fireEvent.change(descriptionInput, { target: { value: 'Test description' } });
    
    expect(descriptionInput).toBeInTheDocument();
  });

  test('should handle NGO selection change for super admin', () => {
    renderWithContext({}, { user: { organisationCategory: 'SUPER_ADMIN' } });
    
    const ngoSelect = screen.getByTestId('select-ngo-name');
    fireEvent.change(ngoSelect, { target: { value: 'org-1' } });
    
    expect(ngoSelect).toBeInTheDocument();
  });

  // Filter handling tests
  test('should populate form with selected filters', () => {
    const selectedFilters = [
      { key: 'nameFilter', value: 'Education Fund' },
      { key: 'descriptionFilter', value: 'Educational support' },
      { key: 'orgIdFilter', value: 'org-1' }
    ];
    
    renderWithContext({ selectedFilters });
    
    expect(mockSetValue).toHaveBeenCalledWith('donationHead', 'Education Fund');
    expect(mockSetValue).toHaveBeenCalledWith('description', 'Educational support');
  });

  test('should handle empty selected filters', () => {
    renderWithContext({ selectedFilters: [] });
    
    expect(mockSetValue).toHaveBeenCalledWith('donationHead', '');
    expect(mockSetValue).toHaveBeenCalledWith('description', '');
  });

  test('should handle null selected filters', () => {
    renderWithContext({ selectedFilters: null });
    
    expect(mockSetValue).toHaveBeenCalledWith('donationHead', '');
    expect(mockSetValue).toHaveBeenCalledWith('description', '');
  });

  test('should handle undefined selected filters', () => {
    renderWithContext({ selectedFilters: undefined });
    
    expect(mockSetValue).toHaveBeenCalledWith('donationHead', '');
    expect(mockSetValue).toHaveBeenCalledWith('description', '');
  });

  // Apply filters functionality
  test('should apply filters with all fields filled', () => {
    // Mock the form submission to include all fields
    mockHandleSubmit.mockImplementation((callback) => (event) => {
      event?.preventDefault();
      callback({
        donationHead: 'Education Fund',
        description: 'Educational support'
      });
    });

    renderWithContext({}, { user: { organisationCategory: 'SUPER_ADMIN' } });
    
    // Simulate NGO selection
    const ngoSelect = screen.getByTestId('select-ngo-name');
    fireEvent.change(ngoSelect, { target: { value: 'org-1' } });
    
    const applyButton = screen.getByTestId('button-contained');
    fireEvent.click(applyButton);
    
    expect(mockOnApplyFilters).toHaveBeenCalledWith([
      { key: 'orgIdFilter', label: 'NGO Name', value: 'org-1' },
      { key: 'nameFilter', label: 'Donation Head', value: 'Education Fund' },
      { key: 'descriptionFilter', label: 'Description', value: 'Educational support' }
    ]);
    expect(mockSetSearchingState).toHaveBeenCalledWith(true);
    expect(mockToggle).toHaveBeenCalled();
  });

  test('should apply filters with only donation head', () => {
    mockHandleSubmit.mockImplementation((callback) => (event) => {
      event?.preventDefault();
      callback({
        donationHead: 'Education Fund'
      });
    });

    renderWithContext();
    
    const applyButton = screen.getByTestId('button-contained');
    fireEvent.click(applyButton);
    
    expect(mockOnApplyFilters).toHaveBeenCalledWith([
      { key: 'nameFilter', label: 'Donation Head', value: 'Education Fund' }
    ]);
  });

  test('should apply empty filters when no fields filled', () => {
    mockHandleSubmit.mockImplementation((callback) => (event) => {
      event?.preventDefault();
      callback({});
    });

    renderWithContext();
    
    const applyButton = screen.getByTestId('button-contained');
    fireEvent.click(applyButton);
    
    expect(mockOnApplyFilters).toHaveBeenCalledWith([]);
  });

  // Edge cases and error handling
  test('should handle component with minimal props', () => {
    const minimalProps = {
      open: true,
      toggle: mockToggle,
      setSearchingState: mockSetSearchingState,
      clearAllFilters: mockClearAllFilters,
      onApplyFilters: mockOnApplyFilters,
    };
    
    render(
      <AuthContext.Provider value={mockAuthContext}>
        <AdvancedSearch {...minimalProps} />
      </AuthContext.Provider>
    );
    
    expect(screen.getByTestId('drawer')).toBeInTheDocument();
  });

  test('should handle empty tenants list', () => {
    renderWithContext({ tenantsList: [] }, { user: { organisationCategory: 'SUPER_ADMIN' } });
    
    const ngoSelect = screen.getByTestId('select-ngo-name');
    expect(ngoSelect).toBeInTheDocument();
    expect(ngoSelect.children).toHaveLength(1); // Only the default "Select..." option
  });

  test('should handle different user categories', () => {
    const categories = ['TENANT', 'SUPER_ADMIN', 'ADMIN'];
    
    categories.forEach(category => {
      const { unmount } = renderWithContext({}, { 
        user: { organisationCategory: category } 
      });
      
      expect(screen.getByTestId('drawer')).toBeInTheDocument();
      unmount();
    });
  });

  test('should handle component unmount', () => {
    const { unmount } = renderWithContext();
    
    expect(() => unmount()).not.toThrow();
  });

  // UI elements tests
  test('should render perfect scrollbar', () => {
    renderWithContext();
    
    expect(screen.getByTestId('perfect-scrollbar')).toBeInTheDocument();
  });

  test('should render all buttons', () => {
    renderWithContext();
    
    expect(screen.getByTestId('button-tonal')).toBeInTheDocument(); // Clear All
    expect(screen.getByTestId('button-contained')).toBeInTheDocument(); // Apply
    expect(screen.getByTestId('icon-button')).toBeInTheDocument(); // Close
  });

  test('should render form controls', () => {
    renderWithContext();
    
    const formControls = screen.getAllByTestId('form-control');
    expect(formControls.length).toBeGreaterThan(0);
  });

  test('should render grid layout', () => {
    renderWithContext();
    
    expect(screen.getByTestId('grid-container')).toBeInTheDocument();
  });

  // Callback function tests
  test('should handle missing callback functions gracefully', () => {
    const propsWithoutCallbacks = {
      open: true,
      toggle: undefined,
      setSearchingState: undefined,
      clearAllFilters: undefined,
      onApplyFilters: undefined,
    };
    
    render(
      <AuthContext.Provider value={mockAuthContext}>
        <AdvancedSearch {...propsWithoutCallbacks} />
      </AuthContext.Provider>
    );
    
    expect(screen.getByTestId('drawer')).toBeInTheDocument();
  });

  // State management tests
  test('should handle form state updates', () => {
    renderWithContext();
    
    // Verify that setValue is called during component initialization
    expect(mockSetValue).toHaveBeenCalled();
  });

  test('should handle form reset', () => {
    renderWithContext();
    
    const clearButton = screen.getByTestId('button-tonal');
    fireEvent.click(clearButton);
    
    expect(mockReset).toHaveBeenCalled();
  });
}); 