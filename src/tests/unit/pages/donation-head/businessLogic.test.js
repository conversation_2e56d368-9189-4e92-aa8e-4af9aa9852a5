/**
 * @jest-environment jsdom
 */

import axios from 'axios';
import { getUrl, getAuthorizationHeaders } from 'src/helpers/utils';
import authConfig from 'src/configs/auth';

// Mock dependencies
jest.mock('axios');
jest.mock('src/helpers/utils');
jest.mock('src/configs/auth', () => ({
  donationHeadEndpoint: '/donation-heads',
  organisationsEndpoint: '/organisations',
  baseURL: 'https://api.test.com',
}));

const mockedAxios = axios;
const mockedGetUrl = getUrl;
const mockedGetAuthorizationHeaders = getAuthorizationHeaders;

describe('Donation Head Business Logic Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockedGetUrl.mockImplementation((endpoint) => `https://api.test.com${endpoint}`);
    mockedGetAuthorizationHeaders.mockReturnValue({
      'Authorization': 'Bearer test-token',
      'Content-Type': 'application/json',
    });
  });

  describe('API Integration Functions', () => {
    describe('fetchUsers function', () => {
      const mockFetchUsers = async (currentPage, currentPageSize, searchKeyword, selectedFilters) => {
        const url = getUrl(authConfig.donationHeadEndpoint) + "/all";
        const headers = getAuthorizationHeaders();
        
        const data = {
          page: currentPage,
          pageSize: currentPageSize,
          searchKeyWord: searchKeyword,
        };
        
        selectedFilters?.forEach((filter) => {
          const key = filter.key;
          data[key] = filter.value;
        });

        try {
          const response = await axios({
            method: "post",
            url: url,
            headers: headers,
            data: data,
          });

          if (response.data) {
            const donationHeads = response.data.donationHeads || [];
            return {
              donationHeads,
              rowCount: response.data?.rowCount || 0,
            };
          } else {
            console.error("Unexpected API response format:", response);
            return { donationHeads: [], rowCount: 0 };
          }
        } catch (error) {
          console.error("Error fetching users:", error);
          throw error;
        }
      };

      test('should fetch donation heads with correct API call parameters', async () => {
        const mockResponse = {
          data: {
            donationHeads: [
              { id: 1, name: 'Education Fund', description: 'Education support' },
              { id: 2, name: 'Healthcare Fund', description: 'Medical assistance' }
            ],
            rowCount: 2
          }
        };

        mockedAxios.mockResolvedValue(mockResponse);

        const result = await mockFetchUsers(1, 10, 'education', []);

        expect(mockedAxios).toHaveBeenCalledWith({
          method: 'post',
          url: 'https://api.test.com/donation-heads/all',
          headers: {
            'Authorization': 'Bearer test-token',
            'Content-Type': 'application/json',
          },
          data: {
            page: 1,
            pageSize: 10,
            searchKeyWord: 'education',
          }
        });

        expect(result).toEqual({
          donationHeads: mockResponse.data.donationHeads,
          rowCount: 2
        });
      });

      test('should include filter parameters in API call', async () => {
        const mockResponse = {
          data: {
            donationHeads: [],
            rowCount: 0
          }
        };

        mockedAxios.mockResolvedValue(mockResponse);

        const filters = [
          { key: 'orgIdFilter', value: 'org-123' },
          { key: 'nameFilter', value: 'test' }
        ];

        await mockFetchUsers(1, 10, '', filters);

        expect(mockedAxios).toHaveBeenCalledWith({
          method: 'post',
          url: 'https://api.test.com/donation-heads/all',
          headers: expect.any(Object),
          data: {
            page: 1,
            pageSize: 10,
            searchKeyWord: '',
            orgIdFilter: 'org-123',
            nameFilter: 'test'
          }
        });
      });

      test('should handle API errors gracefully', async () => {
        const mockError = new Error('Network error');
        mockedAxios.mockRejectedValue(mockError);

        await expect(mockFetchUsers(1, 10, '', [])).rejects.toThrow('Network error');
      });

      test('should handle unexpected response format', async () => {
        const mockResponse = { data: null };
        mockedAxios.mockResolvedValue(mockResponse);

        const result = await mockFetchUsers(1, 10, '', []);

        expect(result).toEqual({
          donationHeads: [],
          rowCount: 0
        });
      });

      test('should handle missing donationHeads in response', async () => {
        const mockResponse = {
          data: {
            rowCount: 5
          }
        };
        mockedAxios.mockResolvedValue(mockResponse);

        const result = await mockFetchUsers(1, 10, '', []);

        expect(result).toEqual({
          donationHeads: [],
          rowCount: 5
        });
      });
    });

    describe('fetchTenants function', () => {
      const mockFetchTenants = async () => {
        try {
          const response = await axios({
            method: "get",
            url: getUrl(authConfig.organisationsEndpoint) + "/TENANT",
            headers: getAuthorizationHeaders(),
          });

          return response.data.map((item) => ({
            value: item.id,
            key: item.name,
          }));
        } catch (error) {
          console.error("Tenants fetch error", error);
          return [];
        }
      };

      test('should fetch tenants list with correct API call', async () => {
        const mockResponse = {
          data: [
            { id: 'org-1', name: 'Organization 1' },
            { id: 'org-2', name: 'Organization 2' }
          ]
        };

        mockedAxios.mockResolvedValue(mockResponse);

        const result = await mockFetchTenants();

        expect(mockedAxios).toHaveBeenCalledWith({
          method: 'get',
          url: 'https://api.test.com/organisations/TENANT',
          headers: {
            'Authorization': 'Bearer test-token',
            'Content-Type': 'application/json',
          }
        });

        expect(result).toEqual([
          { value: 'org-1', key: 'Organization 1' },
          { value: 'org-2', key: 'Organization 2' }
        ]);
      });

      test('should handle tenants fetch error', async () => {
        const mockError = new Error('Fetch failed');
        mockedAxios.mockRejectedValue(mockError);

        const result = await mockFetchTenants();

        expect(result).toEqual([]);
      });
    });
  });

  describe('Delete Operation Business Logic', () => {
    describe('onDelete function', () => {
      const mockOnDelete = async (data) => {
        const url = authConfig.baseURL + authConfig.donationHeadEndpoint + "/" + data.id;
        const headers = getAuthorizationHeaders();

        try {
          if (data?.isActive) {
            await axios.delete(url, { headers });
            return `${data.name} Deactivated successfully.`;
          } else {
            await axios.patch(url, { headers });
            return `${data.name} Activated successfully.`;
          }
        } catch (error) {
          console.error("Delete operation failed", error);
          throw error;
        }
      };

      test('should deactivate active donation head', async () => {
        const mockData = { id: 1, name: 'Test Fund', isActive: true };
        mockedAxios.delete.mockResolvedValue({});

        const result = await mockOnDelete(mockData);

        expect(mockedAxios.delete).toHaveBeenCalledWith(
          'https://api.test.com/donation-heads/1',
          {
            headers: {
              'Authorization': 'Bearer test-token',
              'Content-Type': 'application/json',
            }
          }
        );

        expect(result).toBe('Test Fund Deactivated successfully.');
      });

      test('should activate inactive donation head', async () => {
        const mockData = { id: 2, name: 'Inactive Fund', isActive: false };
        mockedAxios.patch.mockResolvedValue({});

        const result = await mockOnDelete(mockData);

        expect(mockedAxios.patch).toHaveBeenCalledWith(
          'https://api.test.com/donation-heads/2',
          {
            headers: {
              'Authorization': 'Bearer test-token',
              'Content-Type': 'application/json',
            }
          }
        );

        expect(result).toBe('Inactive Fund Activated successfully.');
      });

      test('should handle delete operation errors', async () => {
        const mockData = { id: 1, name: 'Test Fund', isActive: true };
        const mockError = new Error('Delete failed');
        mockedAxios.delete.mockRejectedValue(mockError);

        await expect(mockOnDelete(mockData)).rejects.toThrow('Delete failed');
      });
    });
  });
});
