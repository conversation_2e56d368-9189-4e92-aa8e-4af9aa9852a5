/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import useColumns from 'src/pages/donation-head/Columns';
import { AuthContext } from 'src/context/AuthContext';
import { useRBAC } from 'src/pages/permission/RBACContext';

// Mock dependencies
jest.mock('src/pages/permission/RBACContext');
jest.mock('src/@core/components/mui/chip', () => {
  return function CustomChip({ label, color }) {
    return <span data-testid="chip" style={{ color }}>{label}</span>;
  };
});

jest.mock('src/@core/components/mui/avatar', () => {
  return function CustomAvatar({ children }) {
    return <div data-testid="avatar">{children}</div>;
  };
});

jest.mock('src/@core/components/icon', () => {
  return function Icon({ icon }) {
    return <span data-testid="icon">{icon}</span>;
  };
});

jest.mock('@mui/material', () => ({
  Menu: ({ children, open, anchorEl, onClose }) => 
    open ? <div data-testid="menu" onClick={onClose}>{children}</div> : null,
  MenuItem: ({ children, onClick }) => 
    <div data-testid="menu-item" onClick={onClick}>{children}</div>,
  Tooltip: ({ children, title }) => 
    <div data-testid="tooltip" title={title}>{children}</div>,
}));

describe('useColumns Hook', () => {
  const mockSetMenu = jest.fn();
  const mockSetCurrentRow = jest.fn();
  const mockSetOpenDialog = jest.fn();
  const mockSetOpenDeleteDialog = jest.fn();
  const mockHandleCloseMenuItems = jest.fn();
  const mockSetDonationHeadId = jest.fn();

  const defaultProps = {
    menu: null,
    setMenu: mockSetMenu,
    currentRow: null,
    setCurrentRow: mockSetCurrentRow,
    setOpenDialog: mockSetOpenDialog,
    setOpenDeleteDialog: mockSetOpenDeleteDialog,
    handleCloseMenuItems: mockHandleCloseMenuItems,
    tenantsList: [
      { value: 'org-1', key: 'Test Org 1' },
      { value: 'org-2', key: 'Test Org 2' }
    ],
  };

  const mockAuthContext = {
    donationHeadId: { id: null },
    setDonationHeadId: mockSetDonationHeadId,
    user: { organisationCategory: 'TENANT' }
  };

  const mockSuperAdminContext = {
    donationHeadId: { id: null },
    setDonationHeadId: mockSetDonationHeadId,
    user: { organisationCategory: 'SUPER_ADMIN' }
  };

  const mockCanMenuPageSectionField = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    useRBAC.mockReturnValue({
      canMenuPageSectionField: mockCanMenuPageSectionField
    });
    mockCanMenuPageSectionField.mockReturnValue(true);
  });

  const TestComponent = ({ authContext = mockAuthContext, ...props }) => {
    const columns = useColumns({ ...defaultProps, ...props });
    
    return (
      <div>
        {columns.map((column, index) => (
          <div key={index} data-testid={`column-${column.field}`}>
            {column.headerName}
            {column.renderCell && (
              <div data-testid={`render-cell-${column.field}`}>
                {column.renderCell({ 
                  row: { 
                    id: 1, 
                    name: 'Test Head', 
                    description: 'Test Description',
                    isActive: true,
                    orgId: 'org-1'
                  } 
                })}
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  const renderWithContext = (authContext = mockAuthContext, props = {}) => {
    return render(
      <AuthContext.Provider value={authContext}>
        <TestComponent authContext={authContext} {...props} />
      </AuthContext.Provider>
    );
  };

  test('should render all basic columns', () => {
    renderWithContext();
    
    expect(screen.getByTestId('column-name')).toBeInTheDocument();
    expect(screen.getByTestId('column-description')).toBeInTheDocument();
    expect(screen.getByTestId('column-isActive')).toBeInTheDocument();
    expect(screen.getByTestId('column-actions')).toBeInTheDocument();
  });

  test('should render orgId column for super admin', () => {
    renderWithContext(mockSuperAdminContext);
    
    expect(screen.getByTestId('column-orgId')).toBeInTheDocument();
  });

  test('should not render orgId column for regular users', () => {
    renderWithContext(mockAuthContext);
    
    expect(screen.queryByTestId('column-orgId')).not.toBeInTheDocument();
  });

  test('should render active status chip correctly', () => {
    renderWithContext();
    
    const statusCell = screen.getByTestId('render-cell-isActive');
    expect(statusCell).toBeInTheDocument();
    expect(screen.getByTestId('chip')).toHaveTextContent('Active');
  });

  test('should render inactive status chip correctly', () => {
    const TestComponentInactive = () => {
      const columns = useColumns(defaultProps);
      const statusColumn = columns.find(col => col.field === 'isActive');
      
      return (
        <div>
          {statusColumn.renderCell({ row: { isActive: false } })}
        </div>
      );
    };

    render(
      <AuthContext.Provider value={mockAuthContext}>
        <TestComponentInactive />
      </AuthContext.Provider>
    );
    
    expect(screen.getByTestId('chip')).toHaveTextContent('InActive');
  });

  test('should render menu button in actions column', () => {
    const TestMenuClickComponent = () => {
      const columns = useColumns(defaultProps);
      const actionsColumn = columns.find(col => col.field === 'actions');
      
      return (
        <div>
          {actionsColumn.renderCell({ 
            row: { 
              id: 1, 
              name: 'Test Head', 
              description: 'Test Description',
              isActive: true,
              orgId: 'org-1'
            } 
          })}
        </div>
      );
    };

    render(
      <AuthContext.Provider value={mockAuthContext}>
        <TestMenuClickComponent />
      </AuthContext.Provider>
    );
    
    // Verify the menu button is rendered
    const menuButton = screen.getByTestId('avatar');
    expect(menuButton).toBeInTheDocument();
    
    // Verify the menu is not open by default
    expect(screen.queryByTestId('menu')).not.toBeInTheDocument();
  });

  test('should handle view profile click', () => {
    // Set menu to be open so menu items are rendered
    const propsWithOpenMenu = {
      ...defaultProps,
      menu: document.createElement('div'),
      currentRow: { 
        id: 1, 
        name: 'Test Head', 
        description: 'Test Description',
        isActive: true,
        orgId: 'org-1'
      }
    };
    
    const TestMenuComponent = () => {
      const columns = useColumns(propsWithOpenMenu);
      const actionsColumn = columns.find(col => col.field === 'actions');
      
      return (
        <div>
          {actionsColumn.renderCell({ 
            row: { 
              id: 1, 
              name: 'Test Head', 
              description: 'Test Description',
              isActive: true,
              orgId: 'org-1'
            } 
          })}
        </div>
      );
    };

    render(
      <AuthContext.Provider value={mockAuthContext}>
        <TestMenuComponent />
      </AuthContext.Provider>
    );
    
    // Find and click the view profile menu item
    const menuItems = screen.getAllByTestId('menu-item');
    const viewMenuItem = menuItems.find(item => item.textContent.includes('Edit'));
    
    if (viewMenuItem) {
      fireEvent.click(viewMenuItem);
      expect(mockSetOpenDialog).toHaveBeenCalledWith(true);
      expect(mockHandleCloseMenuItems).toHaveBeenCalled();
    }
  });

  test('should handle toggle status click', () => {
    // Set menu to be open so menu items are rendered
    const propsWithOpenMenu = {
      ...defaultProps,
      menu: document.createElement('div'),
      currentRow: { 
        id: 1, 
        name: 'Test Head', 
        description: 'Test Description',
        isActive: true,
        orgId: 'org-1'
      }
    };
    
    const TestMenuComponent = () => {
      const columns = useColumns(propsWithOpenMenu);
      const actionsColumn = columns.find(col => col.field === 'actions');
      
      return (
        <div>
          {actionsColumn.renderCell({ 
            row: { 
              id: 1, 
              name: 'Test Head', 
              description: 'Test Description',
              isActive: true,
              orgId: 'org-1'
            } 
          })}
        </div>
      );
    };

    render(
      <AuthContext.Provider value={mockAuthContext}>
        <TestMenuComponent />
      </AuthContext.Provider>
    );
    
    // Find and click the toggle status menu item
    const menuItems = screen.getAllByTestId('menu-item');
    const toggleMenuItem = menuItems.find(item => 
      item.textContent.includes('Deactivate') || item.textContent.includes('Activate')
    );
    
    if (toggleMenuItem) {
      fireEvent.click(toggleMenuItem);
      expect(mockSetOpenDeleteDialog).toHaveBeenCalledWith(true);
      expect(mockHandleCloseMenuItems).toHaveBeenCalled();
    }
  });

  test('should render organization name for super admin', () => {
    const TestOrgComponent = () => {
      const columns = useColumns(defaultProps);
      const orgColumn = columns.find(col => col.field === 'orgId');
      
      if (!orgColumn) return <div>No org column</div>;
      
      return (
        <div>
          {orgColumn.renderCell({ row: { orgId: 'org-1' } })}
        </div>
      );
    };

    render(
      <AuthContext.Provider value={mockSuperAdminContext}>
        <TestOrgComponent />
      </AuthContext.Provider>
    );
    
    expect(screen.getByText('Test Org 1')).toBeInTheDocument();
  });

  test('should handle unknown organization ID', () => {
    const TestOrgComponent = () => {
      const columns = useColumns(defaultProps);
      const orgColumn = columns.find(col => col.field === 'orgId');
      
      if (!orgColumn) return <div>No org column</div>;
      
      return (
        <div>
          {orgColumn.renderCell({ row: { orgId: 'unknown-org' } })}
        </div>
      );
    };

    render(
      <AuthContext.Provider value={mockSuperAdminContext}>
        <TestOrgComponent />
      </AuthContext.Provider>
    );
    
    // The component renders empty string for unknown org, not "Unknown"
    expect(screen.getByTestId('tooltip')).toBeInTheDocument();
  });

  test('should handle missing permissions', () => {
    mockCanMenuPageSectionField.mockReturnValue(false);
    
    renderWithContext();
    
    // Should still render basic columns even without permissions
    expect(screen.getByTestId('column-name')).toBeInTheDocument();
  });

  test('should handle unknown status values', () => {
    const TestUnknownStatusComponent = () => {
      const columns = useColumns(defaultProps);
      const statusColumn = columns.find(col => col.field === 'isActive');
      
      return (
        <div>
          {statusColumn.renderCell({ row: { isActive: 'unknown' } })}
        </div>
      );
    };

    render(
      <AuthContext.Provider value={mockAuthContext}>
        <TestUnknownStatusComponent />
      </AuthContext.Provider>
    );
    
    expect(screen.getByTestId('chip')).toHaveTextContent('Unknown');
  });

  test('should render correct action text for inactive items', () => {
    // Set menu to be open so menu items are rendered
    const propsWithOpenMenu = {
      ...defaultProps,
      menu: document.createElement('div'),
      currentRow: { 
        id: 1, 
        name: 'Test Head', 
        description: 'Test Description',
        isActive: false,
        orgId: 'org-1'
      }
    };
    
    const TestInactiveActionsComponent = () => {
      const columns = useColumns(propsWithOpenMenu);
      const actionsColumn = columns.find(col => col.field === 'actions');
      
      return (
        <div>
          {actionsColumn.renderCell({ 
            row: { 
              id: 1, 
              name: 'Test Head', 
              description: 'Test Description',
              isActive: false,
              orgId: 'org-1'
            } 
          })}
        </div>
      );
    };

    render(
      <AuthContext.Provider value={mockAuthContext}>
        <TestInactiveActionsComponent />
      </AuthContext.Provider>
    );
    
    // Should show "Activate" for inactive items
    expect(screen.getByText('Activate')).toBeInTheDocument();
  });

  test('should render correct action text for active items', () => {
    // Set menu to be open so menu items are rendered
    const propsWithOpenMenu = {
      ...defaultProps,
      menu: document.createElement('div'),
      currentRow: { 
        id: 1, 
        name: 'Test Head', 
        description: 'Test Description',
        isActive: true,
        orgId: 'org-1'
      }
    };
    
    const TestActiveActionsComponent = () => {
      const columns = useColumns(propsWithOpenMenu);
      const actionsColumn = columns.find(col => col.field === 'actions');
      
      return (
        <div>
          {actionsColumn.renderCell({ 
            row: { 
              id: 1, 
              name: 'Test Head', 
              description: 'Test Description',
              isActive: true,
              orgId: 'org-1'
            } 
          })}
        </div>
      );
    };

    render(
      <AuthContext.Provider value={mockAuthContext}>
        <TestActiveActionsComponent />
      </AuthContext.Provider>
    );
    
    // Should show "Deactivate" for active items
    expect(screen.getByText('Deactivate')).toBeInTheDocument();
  });
});
