/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import DonationHeadDialog from 'src/pages/donation-head/DonationHeadDialog';
import { AuthContext } from 'src/context/AuthContext';
import { useRBAC } from 'src/pages/permission/RBACContext';

// Mock dependencies
jest.mock('src/pages/permission/RBACContext');
jest.mock('src/hooks/useAuth');

// Mock react-hook-form
const mockSetValue = jest.fn();
const mockWatch = jest.fn(() => ({}));
const mockReset = jest.fn();
const mockHandleSubmit = jest.fn((callback) => callback);
const mockRegister = jest.fn();

jest.mock('react-hook-form', () => ({
  useForm: () => ({
    handleSubmit: mockHandleSubmit,
    setValue: mockSetValue,
    watch: mockWatch,
    reset: mockReset,
    formState: { errors: {} },
    register: mockRegister,
    control: {},
  }),
  Controller: ({ render, name, defaultValue }) => {
    return render({ 
      field: { 
        value: defaultValue || '', 
        onChange: jest.fn(), 
        onBlur: jest.fn(), 
        name 
      }, 
      fieldState: { error: null } 
    });
  },
}));

// Mock useAuth hook
const mockPostDonationHead = jest.fn();
const mockPatchDonationHead = jest.fn();

jest.mock('src/hooks/useAuth', () => ({
  useAuth: () => ({
    postDonationHead: mockPostDonationHead,
    patchDonationHead: mockPatchDonationHead,
  }),
}));

// Mock Material-UI components
jest.mock('@mui/material', () => ({
  Dialog: ({ children, open, onClose }) => open ? (
    <div data-testid="dialog" onClick={onClose}>{children}</div>
  ) : null,
  DialogTitle: ({ children }) => <div data-testid="dialog-title">{children}</div>,
  DialogContent: ({ children }) => <div data-testid="dialog-content">{children}</div>,
  DialogActions: ({ children }) => <div data-testid="dialog-actions">{children}</div>,
  DialogContentText: ({ children }) => <div data-testid="dialog-content-text">{children}</div>,
  FormControl: ({ children }) => <div data-testid="form-control">{children}</div>,
  FormHelperText: ({ children }) => <div>{children}</div>,
  Grid: ({ children }) => <div data-testid="grid">{children}</div>,
  TextField: ({ name, onChange, value, ...props }) => (
    <input 
      data-testid={`textfield-${name}`}
      name={name}
      onChange={onChange}
      value={value}
      {...props}
    />
  ),
  Button: ({ children, onClick, disabled, ...props }) => (
    <button 
      data-testid={`button-${children?.toString().toLowerCase().replace(/\s+/g, '-')}`}
      onClick={onClick}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  ),
  IconButton: ({ children, onClick }) => (
    <button data-testid="icon-button" onClick={onClick}>{children}</button>
  ),
  Box: ({ children }) => <div data-testid="box">{children}</div>,
  CircularProgress: () => <div data-testid="loading-spinner">Loading...</div>,
}));

// Mock custom components
jest.mock('src/@core/components/icon', () => ({ icon }) => <span data-testid="icon">{icon}</span>);

jest.mock('src/@core/components/custom-components/SelectAutoComplete', () => {
  return function MockSelectAutoComplete({ options, onChange, value, ...props }) {
    return (
      <select 
        data-testid="select-autocomplete"
        onChange={(e) => onChange(e.target.value)}
        value={value}
        {...props}
      >
        {options?.map((option) => (
          <option key={option.value} value={option.value}>
            {option.key}
          </option>
        ))}
      </select>
    );
  };
});

jest.mock('src/@core/components/custom-components/NameTextField', () => {
  return function MockNameTextField({ onChange, value, ...props }) {
    return (
      <input 
        data-testid="name-textfield"
        onChange={(e) => onChange(e.target.value)}
        value={value}
        {...props}
      />
    );
  };
});

describe('DonationHeadDialog', () => {
  const mockSetOpenDialog = jest.fn();
  const mockFetchUsers = jest.fn();
  const mockRBAC = {
    canMenuPage: jest.fn(() => true),
    canMenuPageSection: jest.fn(() => true),
    rbacRoles: ['admin'],
  };

  const mockAuthContext = {
    user: {
      id: 1,
      name: 'Test User',
      orgId: 'org123',
      organisationCategory: 'TENANT',
    },
  };

  const defaultProps = {
    open: true,
    onClose: mockSetOpenDialog,
    formData: null,
    fetchUsers: mockFetchUsers,
    page: 1,
    pageSize: 10,
    searchKeyword: '',
    tenantsList: [
      { value: 'tenant1', key: 'Tenant 1' },
      { value: 'tenant2', key: 'Tenant 2' },
    ],
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRBAC.mockReturnValue(mockRBAC);
    mockHandleSubmit.mockImplementation((callback) => (data) => callback(data));
  });

  const renderWithContext = (props = {}, authContext = mockAuthContext) => {
    const mergedProps = { ...defaultProps, ...props };
    return render(
      <AuthContext.Provider value={authContext}>
        <DonationHeadDialog {...mergedProps} />
      </AuthContext.Provider>
    );
  };

  describe('Basic Rendering', () => {
    test('should render dialog when open', () => {
      renderWithContext();
      expect(screen.getByTestId('dialog')).toBeInTheDocument();
    });

    test('should not render dialog when closed', () => {
      renderWithContext({ open: false });
      expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
    });

    test('should render all form fields', () => {
      renderWithContext();
      expect(screen.getByTestId('name-textfield')).toBeInTheDocument();
      expect(screen.getByTestId('textfield-description')).toBeInTheDocument();
    });

    test('should render tenant select for SUPER_ADMIN', () => {
      const superAdminContext = {
        ...mockAuthContext,
        user: { ...mockAuthContext.user, organisationCategory: 'SUPER_ADMIN' }
      };
      renderWithContext({}, superAdminContext);
      expect(screen.getByTestId('select-autocomplete')).toBeInTheDocument();
    });

    test('should not render tenant select for non-SUPER_ADMIN', () => {
      renderWithContext();
      expect(screen.queryByTestId('select-autocomplete')).not.toBeInTheDocument();
    });
  });

  describe('Form Data Population', () => {
    test('should populate form when formData is provided', () => {
      const formData = {
        id: 1,
        name: 'Test Donation Head',
        description: 'Test Description',
        orgId: 'org123'
      };
      renderWithContext({ formData });
      
      expect(mockSetValue).toHaveBeenCalledWith('donationHead', 'Test Donation Head');
      expect(mockSetValue).toHaveBeenCalledWith('description', 'Test Description');
    });

    test('should handle empty formData', () => {
      renderWithContext({ formData: {} });
      
      expect(mockSetValue).toHaveBeenCalledWith('donationHead', '');
      expect(mockSetValue).toHaveBeenCalledWith('description', '');
    });

    test('should handle null formData', () => {
      renderWithContext({ formData: null });
      
      expect(mockSetValue).toHaveBeenCalledWith('donationHead', '');
      expect(mockSetValue).toHaveBeenCalledWith('description', '');
    });
  });

  describe('Form Submission - Create', () => {
    test('should call postDonationHead for new donation head', async () => {
      const user = userEvent.setup();
      mockPostDonationHead.mockResolvedValue({ success: true });
      
      renderWithContext();
      
      const saveButton = screen.getByTestId('button-save');
      await user.click(saveButton);
      
      await waitFor(() => {
        expect(mockPostDonationHead).toHaveBeenCalledWith(
          expect.objectContaining({
            name: undefined, // Will be undefined as we're not setting form data
            orgId: 'org123',
            description: undefined,
          }),
          expect.any(Function), // handleFailure
          expect.any(Function)  // handleSuccess
        );
      });
    });

    test('should handle create success', async () => {
      const user = userEvent.setup();
      mockPostDonationHead.mockImplementation((fields, handleFailure, handleSuccess) => {
        handleSuccess();
        return Promise.resolve();
      });
      
      renderWithContext();
      
      const saveButton = screen.getByTestId('button-save');
      await user.click(saveButton);
      
      await waitFor(() => {
        expect(mockFetchUsers).toHaveBeenCalledWith(1, 10, '');
        expect(mockSetOpenDialog).toHaveBeenCalled();
      });
    });

    test('should handle create failure', async () => {
      const user = userEvent.setup();
      mockPostDonationHead.mockImplementation((fields, handleFailure, handleSuccess) => {
        handleFailure();
        return Promise.resolve();
      });
      
      renderWithContext();
      
      const saveButton = screen.getByTestId('button-save');
      await user.click(saveButton);
      
      // Should still close dialog and fetch users even on failure
      await waitFor(() => {
        expect(mockFetchUsers).toHaveBeenCalledWith(1, 10, '');
        expect(mockSetOpenDialog).toHaveBeenCalled();
      });
    });

    test('should handle API exception during create', async () => {
      const user = userEvent.setup();
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      mockPostDonationHead.mockRejectedValue(new Error('API Error'));
      
      renderWithContext();
      
      const saveButton = screen.getByTestId('button-save');
      await user.click(saveButton);
      
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('Employee Creation failed:', expect.any(Error));
        expect(mockFetchUsers).toHaveBeenCalledWith(1, 10, '');
        expect(mockSetOpenDialog).toHaveBeenCalled();
      });
      
      consoleSpy.mockRestore();
    });
  });

  describe('Form Submission - Update', () => {
    test('should call patchDonationHead for existing donation head', async () => {
      const user = userEvent.setup();
      const formData = { id: 1, name: 'Existing Head', description: 'Existing Desc' };
      mockPatchDonationHead.mockResolvedValue({ success: true });
      
      renderWithContext({ formData });
      
      const updateButton = screen.getByTestId('button-update');
      await user.click(updateButton);
      
      await waitFor(() => {
        expect(mockPatchDonationHead).toHaveBeenCalledWith(
          1, // formData.id
          expect.objectContaining({
            name: undefined,
            orgId: 'org123',
            description: undefined,
          }),
          expect.any(Function), // handleFailureUpdate
          expect.any(Function)  // handleSuccessUpdate
        );
      });
    });

    test('should handle update success', async () => {
      const user = userEvent.setup();
      const formData = { id: 1, name: 'Existing Head' };
      mockPatchDonationHead.mockImplementation((id, fields, handleFailure, handleSuccess) => {
        handleSuccess();
        return Promise.resolve();
      });
      
      renderWithContext({ formData });
      
      const updateButton = screen.getByTestId('button-update');
      await user.click(updateButton);
      
      await waitFor(() => {
        expect(mockFetchUsers).toHaveBeenCalledWith(1, 10, '');
        expect(mockSetOpenDialog).toHaveBeenCalled();
      });
    });

    test('should handle update failure', async () => {
      const user = userEvent.setup();
      const formData = { id: 1, name: 'Existing Head' };
      mockPatchDonationHead.mockImplementation((id, fields, handleFailure, handleSuccess) => {
        handleFailure();
        return Promise.resolve();
      });
      
      renderWithContext({ formData });
      
      const updateButton = screen.getByTestId('button-update');
      await user.click(updateButton);
      
      await waitFor(() => {
        expect(mockFetchUsers).toHaveBeenCalledWith(1, 10, '');
        expect(mockSetOpenDialog).toHaveBeenCalled();
      });
    });

    test('should handle API exception during update', async () => {
      const user = userEvent.setup();
      const formData = { id: 1, name: 'Existing Head' };
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      mockPatchDonationHead.mockRejectedValue(new Error('Update Error'));
      
      renderWithContext({ formData });
      
      const updateButton = screen.getByTestId('button-update');
      await user.click(updateButton);
      
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('Employee Creation failed:', expect.any(Error));
        expect(mockFetchUsers).toHaveBeenCalledWith(1, 10, '');
        expect(mockSetOpenDialog).toHaveBeenCalled();
      });
      
      consoleSpy.mockRestore();
    });
  });

  describe('Organization-specific Logic', () => {
    test('should use tenantId for SUPER_ADMIN', async () => {
      const user = userEvent.setup();
      const superAdminContext = {
        ...mockAuthContext,
        user: { ...mockAuthContext.user, organisationCategory: 'SUPER_ADMIN' }
      };
      mockPostDonationHead.mockResolvedValue({ success: true });
      
      renderWithContext({}, superAdminContext);
      
      // Wait for component to render with tenant select
      await waitFor(() => {
        expect(screen.getByTestId('select-autocomplete')).toBeInTheDocument();
      });
      
      // For now, just test that the component accepts SUPER_ADMIN context
      // The actual tenant selection logic might need mock updating
      const saveButton = screen.getByTestId('button-save');
      await user.click(saveButton);
      
      await waitFor(() => {
        expect(mockPostDonationHead).toHaveBeenCalledWith(
          expect.objectContaining({
            orgId: expect.any(String), // Should use either default or selected tenantId
          }),
          expect.any(Function),
          expect.any(Function)
        );
      });
    });

    test('should use user orgId for non-SUPER_ADMIN', async () => {
      const user = userEvent.setup();
      mockPostDonationHead.mockResolvedValue({ success: true });
      
      renderWithContext();
      
      const saveButton = screen.getByTestId('button-save');
      await user.click(saveButton);
      
      await waitFor(() => {
        expect(mockPostDonationHead).toHaveBeenCalledWith(
          expect.objectContaining({
            orgId: 'org123', // Should use user's orgId
          }),
          expect.any(Function),
          expect.any(Function)
        );
      });
    });
  });

  describe('Dialog Management', () => {
    test('should handle close button click', () => {
      renderWithContext();
      
      const iconButton = screen.getByTestId('icon-button');
      fireEvent.click(iconButton);
      
      expect(mockSetOpenDialog).toHaveBeenCalled();
    });

    test('should handle cancel button click', () => {
      renderWithContext();
      
      const cancelButton = screen.getByTestId('button-cancel');
      fireEvent.click(cancelButton);
      
      expect(mockSetOpenDialog).toHaveBeenCalled();
    });

    test('should handle dialog backdrop click', () => {
      renderWithContext();
      
      const dialog = screen.getByTestId('dialog');
      fireEvent.click(dialog);
      
      expect(mockSetOpenDialog).toHaveBeenCalled();
    });
  });

  describe('Loading States', () => {
    test('should show loading spinner during submission', async () => {
      const user = userEvent.setup();
      mockPostDonationHead.mockImplementation(() => new Promise(resolve => 
        setTimeout(() => resolve({ success: true }), 100)
      ));
      
      renderWithContext();
      
      const saveButton = screen.getByTestId('button-save');
      await user.click(saveButton);
      
      // Should show loading spinner during request
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });

    test('should disable buttons during loading', async () => {
      const user = userEvent.setup();
      mockPostDonationHead.mockImplementation(() => new Promise(resolve => 
        setTimeout(() => resolve({ success: true }), 100)
      ));
      
      renderWithContext();
      
      const saveButton = screen.getByTestId('button-save');
      await user.click(saveButton);
      
      // The loading state might not be easily testable with current mocks
      // Test that the function was called instead
      await waitFor(() => {
        expect(mockPostDonationHead).toHaveBeenCalled();
      });
    });
  });

  describe('Edge Cases', () => {
    test('should handle undefined onClose', () => {
      const props = { ...defaultProps, onClose: undefined };
      
      expect(() => {
        render(
          <AuthContext.Provider value={mockAuthContext}>
            <DonationHeadDialog {...props} />
          </AuthContext.Provider>
        );
      }).not.toThrow();
    });

    test('should handle undefined fetchUsers', () => {
      const props = { ...defaultProps, fetchUsers: undefined };
      
      expect(() => {
        render(
          <AuthContext.Provider value={mockAuthContext}>
            <DonationHeadDialog {...props} />
          </AuthContext.Provider>
        );
      }).not.toThrow();
    });

    test('should handle missing user context', () => {
      const emptyAuthContext = { user: null };
      
      expect(() => {
        renderWithContext({}, emptyAuthContext);
      }).not.toThrow();
    });

    test('should handle different organization categories', () => {
      const organizationCategories = ['TENANT', 'SUPER_ADMIN', 'ADMIN', 'NGO'];
      
      organizationCategories.forEach(category => {
        const authContext = {
          ...mockAuthContext,
          user: { ...mockAuthContext.user, organisationCategory: category }
        };
        
        const { unmount } = renderWithContext({}, authContext);
        expect(screen.getByTestId('dialog')).toBeInTheDocument();
        unmount();
      });
    });
  });

  describe('Confirmation Dialog', () => {
    test('should show confirmation dialog on success', async () => {
      const user = userEvent.setup();
      mockPostDonationHead.mockImplementation((fields, handleFailure, handleSuccess) => {
        handleSuccess();
        return Promise.resolve();
      });
      
      renderWithContext();
      
      const saveButton = screen.getByTestId('button-save');
      await user.click(saveButton);
      
      await waitFor(() => {
        // Should have two dialogs now - main dialog and confirmation dialog
        const dialogs = screen.getAllByTestId('dialog');
        expect(dialogs).toHaveLength(2);
      });
    });

    test('should close confirmation dialog on okay click', async () => {
      const user = userEvent.setup();
      mockPostDonationHead.mockImplementation((fields, handleFailure, handleSuccess) => {
        handleSuccess();
        return Promise.resolve();
      });
      
      renderWithContext();
      
      const saveButton = screen.getByTestId('button-save');
      await user.click(saveButton);
      
      await waitFor(() => {
        const okayButton = screen.getByTestId('button-okay');
        expect(okayButton).toBeInTheDocument();
      });
      
      const okayButton = screen.getByTestId('button-okay');
      await user.click(okayButton);
      
      // Confirmation dialog should close but we can't easily test this
      // without more complex state management mocking
    });
  });
});
