/**
 * @jest-environment jsdom
 */

describe('Donation Head Columns Logic Tests', () => {
  describe('Status Mapping Logic', () => {
    describe('mapIsActiveToLabel function', () => {
      const userStatusObj = {
        true: "Active",
        false: "InActive",
      };

      const mapIsActiveToLabel = (isActive) => {
        return userStatusObj[isActive] || "Unknown";
      };

      test('should map boolean true to Active', () => {
        expect(mapIsActiveToLabel(true)).toBe('Active');
      });

      test('should map boolean false to InActive', () => {
        expect(mapIsActiveToLabel(false)).toBe('InActive');
      });

      test('should map string "true" to Active', () => {
        expect(mapIsActiveToLabel("true")).toBe('Active');
      });

      test('should map string "false" to InActive', () => {
        expect(mapIsActiveToLabel("false")).toBe('InActive');
      });

      test('should map unknown values to Unknown', () => {
        expect(mapIsActiveToLabel(null)).toBe('Unknown');
        expect(mapIsActiveToLabel(undefined)).toBe('Unknown');
        expect(mapIsActiveToLabel('invalid')).toBe('Unknown');
        expect(mapIsActiveToLabel(123)).toBe('Unknown');
        expect(mapIsActiveToLabel({})).toBe('Unknown');
      });
    });
  });

  describe('Organization Display Logic', () => {
    describe('findOrganizationName function', () => {
      const findOrganizationName = (tenantsList, orgId) => {
        const org = tenantsList?.find((item) => item?.value === orgId);
        return org ? (org?.key || "") : "";
      };

      test('should find organization name by ID', () => {
        const tenantsList = [
          { value: 'org-1', key: 'Organization 1' },
          { value: 'org-2', key: 'Organization 2' },
          { value: 'org-3', key: 'Organization 3' }
        ];

        expect(findOrganizationName(tenantsList, 'org-2')).toBe('Organization 2');
        expect(findOrganizationName(tenantsList, 'org-1')).toBe('Organization 1');
        expect(findOrganizationName(tenantsList, 'org-3')).toBe('Organization 3');
      });

      test('should return empty string for non-existent organization', () => {
        const tenantsList = [
          { value: 'org-1', key: 'Organization 1' }
        ];

        expect(findOrganizationName(tenantsList, 'org-999')).toBe('');
        expect(findOrganizationName(tenantsList, null)).toBe('');
        expect(findOrganizationName(tenantsList, undefined)).toBe('');
      });

      test('should handle empty or null tenants list', () => {
        expect(findOrganizationName([], 'org-1')).toBe('');
        expect(findOrganizationName(null, 'org-1')).toBe('');
        expect(findOrganizationName(undefined, 'org-1')).toBe('');
      });

      test('should handle malformed tenant objects', () => {
        const tenantsList = [
          { value: 'org-1' }, // missing key
          { key: 'Organization 2' }, // missing value
          { value: 'org-3', key: 'Organization 3' } // complete
        ];

        expect(findOrganizationName(tenantsList, 'org-1')).toBe('');
        expect(findOrganizationName(tenantsList, 'org-2')).toBe('');
        expect(findOrganizationName(tenantsList, 'org-3')).toBe('Organization 3');
      });
    });
  });

  describe('Date Formatting Logic', () => {
    describe('formatDateValue function', () => {
      const formatDateValue = (dateString) => {
        if (!dateString) return '';
        return dateString.split("T")[0];
      };

      test('should format ISO date strings correctly', () => {
        expect(formatDateValue('2023-12-25T10:30:00Z')).toBe('2023-12-25');
        expect(formatDateValue('2024-01-01T00:00:00.000Z')).toBe('2024-01-01');
        expect(formatDateValue('2023-06-15T14:45:30.123Z')).toBe('2023-06-15');
      });

      test('should handle date strings without time component', () => {
        expect(formatDateValue('2023-12-25')).toBe('2023-12-25');
        expect(formatDateValue('2024-01-01')).toBe('2024-01-01');
      });

      test('should handle invalid or empty date strings', () => {
        expect(formatDateValue('')).toBe('');
        expect(formatDateValue(null)).toBe('');
        expect(formatDateValue(undefined)).toBe('');
      });

      test('should handle malformed date strings', () => {
        expect(formatDateValue('invalid-date')).toBe('invalid-date');
        expect(formatDateValue('2023')).toBe('2023');
        expect(formatDateValue('not-a-date-string')).toBe('not-a-date-string');
      });
    });
  });

  describe('Column Configuration Logic', () => {
    describe('buildColumnConfig function', () => {
      const buildColumnConfig = (user, tenantsList) => {
        const baseColumns = [
          {
            field: "name",
            headerName: "Name",
            flex: 0.13,
            minWidth: 120,
          },
          {
            field: "description",
            headerName: "Description",
            flex: 0.13,
            minWidth: 120,
          },
          {
            field: "createdOn",
            headerName: "Created On",
            flex: 0.07,
            minWidth: 120,
            valueFormatter: (params) => params.value?.split("T")[0],
          },
          {
            field: "updatedOn",
            headerName: "Updated On",
            flex: 0.07,
            minWidth: 120,
            valueFormatter: (params) => params.value?.split("T")[0],
          },
          {
            field: "isActive",
            headerName: "Status",
            flex: 0.07,
            minWidth: 100,
          },
          {
            field: "actions",
            headerName: "Actions",
            flex: 0.05,
            minWidth: 50,
            sortable: false,
          }
        ];

        // Add organization column for super admin
        if (user?.organisationCategory === "SUPER_ADMIN") {
          const orgColumn = {
            field: "orgId",
            headerName: "NGO Name",
            flex: 0.13,
            minWidth: 120,
          };
          baseColumns.splice(1, 0, orgColumn);
        }

        return baseColumns.filter(Boolean);
      };

      test('should build columns for super admin with organization column', () => {
        const user = { organisationCategory: 'SUPER_ADMIN' };
        const tenantsList = [
          { value: 'org-1', key: 'Organization 1' }
        ];

        const columns = buildColumnConfig(user, tenantsList);

        expect(columns).toHaveLength(7);
        expect(columns[1].field).toBe('orgId');
        expect(columns[1].headerName).toBe('NGO Name');
      });

      test('should build columns for regular user without organization column', () => {
        const user = { organisationCategory: 'NGO' };
        const tenantsList = [];

        const columns = buildColumnConfig(user, tenantsList);

        expect(columns).toHaveLength(6);
        expect(columns.find(col => col.field === 'orgId')).toBeUndefined();
      });

      test('should handle null user', () => {
        const columns = buildColumnConfig(null, []);

        expect(columns).toHaveLength(6);
        expect(columns.find(col => col.field === 'orgId')).toBeUndefined();
      });
    });
  });

  describe('Action Menu Logic', () => {
    describe('determineMenuActions function', () => {
      const determineMenuActions = (currentRow, permissions) => {
        const actions = [];

        if (permissions.canEdit) {
          actions.push({
            label: 'Edit',
            action: 'edit',
            enabled: true
          });
        }

        if (permissions.canToggleStatus) {
          actions.push({
            label: currentRow?.isActive ? 'Deactivate' : 'Activate',
            action: 'toggleStatus',
            enabled: true
          });
        }

        return actions;
      };

      test('should include edit action when user has edit permission', () => {
        const currentRow = { id: 1, name: 'Test Fund', isActive: true };
        const permissions = { canEdit: true, canToggleStatus: false };

        const actions = determineMenuActions(currentRow, permissions);

        expect(actions).toContainEqual({
          label: 'Edit',
          action: 'edit',
          enabled: true
        });
      });

      test('should include deactivate action for active items', () => {
        const currentRow = { id: 1, name: 'Test Fund', isActive: true };
        const permissions = { canEdit: false, canToggleStatus: true };

        const actions = determineMenuActions(currentRow, permissions);

        expect(actions).toContainEqual({
          label: 'Deactivate',
          action: 'toggleStatus',
          enabled: true
        });
      });

      test('should include activate action for inactive items', () => {
        const currentRow = { id: 1, name: 'Test Fund', isActive: false };
        const permissions = { canEdit: false, canToggleStatus: true };

        const actions = determineMenuActions(currentRow, permissions);

        expect(actions).toContainEqual({
          label: 'Activate',
          action: 'toggleStatus',
          enabled: true
        });
      });

      test('should return empty actions when no permissions', () => {
        const currentRow = { id: 1, name: 'Test Fund', isActive: true };
        const permissions = { canEdit: false, canToggleStatus: false };

        const actions = determineMenuActions(currentRow, permissions);

        expect(actions).toEqual([]);
      });

      test('should include all actions when user has all permissions', () => {
        const currentRow = { id: 1, name: 'Test Fund', isActive: true };
        const permissions = { canEdit: true, canToggleStatus: true };

        const actions = determineMenuActions(currentRow, permissions);

        expect(actions).toHaveLength(2);
        expect(actions).toContainEqual({
          label: 'Edit',
          action: 'edit',
          enabled: true
        });
        expect(actions).toContainEqual({
          label: 'Deactivate',
          action: 'toggleStatus',
          enabled: true
        });
      });
    });
  });

  describe('Permission Checking Logic', () => {
    describe('checkFieldPermission function', () => {
      const checkFieldPermission = (userRole, requiredPermission, field) => {
        const fieldPermissions = {
          'admin': {
            'Edit': ['FULL_ACCESS'],
            'Activate_Or_Deactivate': ['FULL_ACCESS'],
            'Name': ['READ', 'FULL_ACCESS'],
            'Description': ['READ', 'FULL_ACCESS']
          },
          'editor': {
            'Edit': ['FULL_ACCESS'],
            'Name': ['READ'],
            'Description': ['READ']
          },
          'viewer': {
            'Name': ['READ'],
            'Description': ['READ']
          }
        };

        const userPermissions = fieldPermissions[userRole] || {};
        const fieldPerms = userPermissions[field] || [];
        
        return fieldPerms.includes(requiredPermission);
      };

      test('should check admin permissions correctly', () => {
        expect(checkFieldPermission('admin', 'FULL_ACCESS', 'Edit')).toBe(true);
        expect(checkFieldPermission('admin', 'FULL_ACCESS', 'Activate_Or_Deactivate')).toBe(true);
        expect(checkFieldPermission('admin', 'READ', 'Name')).toBe(true);
        expect(checkFieldPermission('admin', 'FULL_ACCESS', 'Name')).toBe(true);
      });

      test('should check editor permissions correctly', () => {
        expect(checkFieldPermission('editor', 'FULL_ACCESS', 'Edit')).toBe(true);
        expect(checkFieldPermission('editor', 'READ', 'Name')).toBe(true);
        expect(checkFieldPermission('editor', 'FULL_ACCESS', 'Activate_Or_Deactivate')).toBe(false);
      });

      test('should check viewer permissions correctly', () => {
        expect(checkFieldPermission('viewer', 'READ', 'Name')).toBe(true);
        expect(checkFieldPermission('viewer', 'READ', 'Description')).toBe(true);
        expect(checkFieldPermission('viewer', 'FULL_ACCESS', 'Edit')).toBe(false);
        expect(checkFieldPermission('viewer', 'FULL_ACCESS', 'Activate_Or_Deactivate')).toBe(false);
      });

      test('should handle unknown roles', () => {
        expect(checkFieldPermission('unknown', 'READ', 'Name')).toBe(false);
        expect(checkFieldPermission(null, 'READ', 'Name')).toBe(false);
        expect(checkFieldPermission(undefined, 'READ', 'Name')).toBe(false);
      });

      test('should handle unknown fields', () => {
        expect(checkFieldPermission('admin', 'READ', 'UnknownField')).toBe(false);
        expect(checkFieldPermission('admin', 'FULL_ACCESS', 'UnknownField')).toBe(false);
      });
    });
  });
});
