# Donation Head Business Logic Tests

This directory contains comprehensive Jest unit tests for the business logic in the donation-head module. These tests focus exclusively on business logic, data processing, API integration, and utility functions, **excluding React component rendering and UI interactions**.

## 📁 Test Files Overview

### 1. `businessLogic.test.js`
**Focus**: Core API integration and data fetching logic
- `fetchUsers` function with various parameters and error scenarios
- `fetchTenants` function with error handling
- API response processing and error handling
- Network error scenarios and edge cases

### 2. `formLogic.test.js`
**Focus**: Form submission and data processing logic
- Create donation head submission (`submit` function)
- Update donation head submission (`update` function)
- Organization ID determination for different user types (SUPER_ADMIN vs regular users)
- Form data validation and error handling
- API integration with success/failure callbacks

### 3. `filterLogic.test.js`
**Focus**: Search and filter management logic
- Filter creation from form data (`handleApplyFilters`)
- Filter removal logic (`handleRemoveFilter`)
- Search keyword processing and validation
- Filter-to-API data mapping
- Pagination logic and state management

### 4. `validationLogic.test.js`
**Focus**: Data validation and error handling
- Form field validation (donation head name, description, tenant selection)
- Business rule validation (required fields, length limits)
- API error processing and categorization (400, 401, 403, 500 errors)
- Success message generation for different operations
- Data integrity validation

### 5. `utilityLogic.test.js`
**Focus**: Utility functions and data transformations
- Status mapping (`mapIsActiveToLabel`)
- Date formatting utilities
- Tenant data transformation
- Permission checking utilities
- Organization category logic
- State management utilities
- URL and API endpoint construction

### 6. `advancedSearchLogic.test.js`
**Focus**: Advanced search functionality
- Filter mapping to form values
- Search state management and transitions
- Search filter validation
- Search query building for different user types
- Search result processing and formatting

### 7. `columnsLogic.test.js`
**Focus**: Data grid column logic and display formatting
- Status mapping for display
- Organization name lookup and display
- Date formatting for column display
- Column configuration based on user type (SUPER_ADMIN vs regular)
- Action menu logic and permission-based actions

## 🎯 Test Coverage Goals

### Primary Focus Areas
- **API Integration**: 95%+ coverage of API calls and error handling
- **Data Processing**: 100% coverage of data transformation functions
- **Business Rules**: 100% coverage of validation and business logic
- **State Management**: 95%+ coverage of filter and search state logic
- **Utility Functions**: 100% coverage of helper and utility functions

### Edge Cases Covered
- Network errors and API failures
- Invalid or malformed data inputs
- Permission-based logic variations
- Empty, null, and undefined data handling
- User type variations (SUPER_ADMIN vs regular users)
- Boundary conditions (min/max lengths, limits)

## 🚀 Running the Tests

### Run All Business Logic Tests
```bash
# From project root
npm run test:donation-head

# Or run the custom test runner
node src/tests/unit/pages/donation-head/runBusinessLogicTests.js
```

### Run Individual Test Files
```bash
# API integration tests
npm test src/tests/unit/pages/donation-head/businessLogic.test.js

# Form logic tests
npm test src/tests/unit/pages/donation-head/formLogic.test.js

# Filter logic tests
npm test src/tests/unit/pages/donation-head/filterLogic.test.js

# Validation logic tests
npm test src/tests/unit/pages/donation-head/validationLogic.test.js

# Utility logic tests
npm test src/tests/unit/pages/donation-head/utilityLogic.test.js

# Advanced search logic tests
npm test src/tests/unit/pages/donation-head/advancedSearchLogic.test.js

# Columns logic tests
npm test src/tests/unit/pages/donation-head/columnsLogic.test.js
```

### Run with Coverage
```bash
npm test src/tests/unit/pages/donation-head/ --coverage
```

## 📊 Test Metrics

### Test Categories
- **API Integration Tests**: 15+ test cases
- **Form Logic Tests**: 20+ test cases  
- **Filter Logic Tests**: 25+ test cases
- **Validation Logic Tests**: 30+ test cases
- **Utility Logic Tests**: 35+ test cases
- **Advanced Search Tests**: 20+ test cases
- **Columns Logic Tests**: 25+ test cases

### Coverage Targets
- **Statements**: 85%+
- **Branches**: 85%+
- **Functions**: 85%+
- **Lines**: 85%+

## 🧪 Test Patterns and Best Practices

### Mocking Strategy
- **External Dependencies**: Fully mocked (axios, helpers, configs)
- **React Components**: Not tested (business logic focus)
- **Context/Hooks**: Mocked with realistic return values

### Test Structure
```javascript
describe('Feature Category', () => {
  describe('Specific Function/Logic', () => {
    test('should handle normal case', () => {
      // Test implementation
    });
    
    test('should handle edge case', () => {
      // Edge case testing
    });
    
    test('should handle error scenario', () => {
      // Error handling testing
    });
  });
});
```

### Assertion Patterns
- **Exact matching** for critical business logic
- **Partial matching** for complex objects where appropriate
- **Error boundary testing** for all error scenarios
- **Type checking** for function return values

## 🔍 Key Business Logic Areas Tested

### 1. User Type Handling
- SUPER_ADMIN vs regular user logic
- Organization ID determination
- Permission-based feature access

### 2. Data Validation
- Required field validation
- Length and format validation
- Business rule enforcement

### 3. API Integration
- Request parameter construction
- Response data processing
- Error handling and recovery

### 4. State Management
- Filter state transitions
- Search state management
- Pagination state logic

### 5. Data Transformation
- API response to UI data mapping
- Form data to API request mapping
- Display value formatting

## 📝 Notes

### What's NOT Tested
- React component rendering
- UI interactions and events
- DOM manipulation
- Component lifecycle methods
- Material-UI component behavior

### What IS Tested
- Pure functions and business logic
- API integration functions
- Data processing and transformation
- Validation rules and error handling
- State management logic
- Utility and helper functions

### Test Environment
- **Jest Environment**: jsdom (for localStorage and basic DOM APIs)
- **Mocking**: Comprehensive mocking of external dependencies
- **Isolation**: Each test file is independent and can run in isolation
- **Performance**: Fast execution with minimal setup/teardown

## 🎯 Future Enhancements

1. **Performance Testing**: Add tests for large dataset handling
2. **Integration Testing**: Add tests that combine multiple business logic functions
3. **Stress Testing**: Add tests for concurrent operations and race conditions
4. **Security Testing**: Add tests for input sanitization and validation bypass attempts
