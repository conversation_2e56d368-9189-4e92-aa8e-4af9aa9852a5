import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import axios from 'axios';
import LongFormDialog from 'src/pages/profile/sections-longform/LongFormDialog';
import { AuthProvider, AuthContext } from 'src/context/AuthContext';

// Mock dependencies
jest.mock('axios');
jest.mock('src/helpers/utils', () => ({
  getUrl: jest.fn((endpoint) => `https://api.test.com${endpoint}`),
  getAuthorizationHeaders: jest.fn(() => ({
    'Authorization': 'Bearer test-token',
    'Content-Type': 'application/json',
  })),
  getFileUploadHeaders: jest.fn(() => ({
    'Authorization': 'Bearer test-token',
    'Content-Type': 'multipart/form-data',
  })),
}));

jest.mock('src/@core/components/custom-components/SelectAutoComplete', () => {
  return function MockSelectAutoComplete({ name, label, options, onChange, value, ...props }) {
    return (
      <select
        data-testid={`select-${name}`}
        value={value || ''}
        onChange={(e) => onChange && onChange({ target: { value: e.target.value } })}
        {...props}
      >
        <option value="">{label}</option>
        {options?.map((option) => (
          <option key={option.value} value={option.value}>
            {option.key}
          </option>
        ))}
      </select>
    );
  };
});

// Mock required utilities
jest.mock('src/hooks/useRuntimeConfig', () => ({
  useRuntimeConfig: () => ({
    getConfigValue: jest.fn(() => 'test-value'),
  }),
}));

describe('LongFormDialog', () => {
  const mockAuthContext = {
    user: {
      id: 1,
      orgId: 123,
      role: 'admin',
    },
    listValues: [
      { id: 1, name: 'Option 1' },
      { id: 2, name: 'Option 2' },
    ],
    getAllListValuesByListNameId: jest.fn(),
    patchCHSProfile: jest.fn(),
  };

  const defaultProps = {
    open: true,
    handleClose: jest.fn(),
    currentRow: null,
    fetchTenants: jest.fn(),
    page: 1,
    pageSize: 10,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    axios.mockResolvedValue({ data: { success: true } });
  });

  const renderDialog = (props = {}, contextOverrides = {}) => {
    const authContextValue = { ...mockAuthContext, ...contextOverrides };
    const dialogProps = { ...defaultProps, ...props };
    
    return render(
      <AuthProvider value={authContextValue}>
        <LongFormDialog {...dialogProps} />
      </AuthProvider>
    );
  };

  describe('Dialog Rendering Tests', () => {
    test('should render dialog when open', () => {
      renderDialog();

      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('NGO Profile')).toBeInTheDocument();
    });

    test('should not render dialog when closed', () => {
      renderDialog({ open: false });

      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });

    test('should render close button', () => {
      renderDialog();

      const closeButton = screen.getByRole('button', { name: '' });
      expect(closeButton).toBeInTheDocument();
    });
  });

  describe('Dialog Actions Tests', () => {
    test('should handle close via X button', async () => {
      const user = userEvent.setup();
      const handleClose = jest.fn();
      renderDialog({ handleClose });

      const closeButton = screen.getByRole('button', { name: '' });
      await user.click(closeButton);

      expect(handleClose).toHaveBeenCalled();
    });
  });

  describe('Error Handling Tests', () => {
    test('should handle component errors gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      renderDialog();

      expect(screen.getByRole('dialog')).toBeInTheDocument();
      consoleSpy.mockRestore();
    });
  });
}); 