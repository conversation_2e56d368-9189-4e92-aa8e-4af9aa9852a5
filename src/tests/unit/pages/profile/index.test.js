import React from 'react';
import { render, screen } from '@testing-library/react';
import { useRouter } from 'next/router';
import RegistrationForm from 'src/pages/profile/index';
import { AuthProvider, AuthContext } from 'src/context/AuthContext';
import { useRBAC } from 'src/pages/permission/RBACContext';

// Mock dependencies
jest.mock('src/helpers/utils', () => ({
  getUrl: jest.fn((endpoint) => `https://api.test.com${endpoint}`),
  getAuthorizationHeaders: jest.fn(() => ({
    'Authorization': 'Bearer test-token',
    'Content-Type': 'application/json',
  })),
}));

jest.mock('src/pages/permission/RBACContext', () => ({
  useRBAC: jest.fn(),
}));

jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

describe('RegistrationForm', () => {
  const mockAuthContext = {
    user: {
      id: 1,
      orgId: 123,
      role: 'admin',
    },
    listValues: [],
    getAllListValuesByListNameId: jest.fn(),
  };

  const mockRouter = {
    push: jest.fn(),
    pathname: '/profile',
    query: {},
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockReturnValue(mockRouter);
    useRBAC.mockReturnValue({
      hasPermission: jest.fn(() => true),
      permissions: [],
      canMenuPage: jest.fn(() => true),
      rbacRoles: ['admin'],
    });
  });

  const renderComponent = (contextOverrides = {}) => {
    const authContextValue = { ...mockAuthContext, ...contextOverrides };
    
    return render(
      <AuthProvider value={authContextValue}>
        <RegistrationForm />
      </AuthProvider>
    );
  };

  describe('Basic Rendering Tests', () => {
    test('should render without crashing', () => {
      renderComponent();
      // Just check that the component renders without throwing an error
      expect(document.body).toBeInTheDocument();
    });

    test('should render with auth context', () => {
      renderComponent();
      expect(document.body).toBeInTheDocument();
    });
  });
}); 