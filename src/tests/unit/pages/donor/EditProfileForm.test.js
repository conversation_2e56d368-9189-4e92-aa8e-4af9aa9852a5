import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import axios from 'axios';
import EditProfileForm from 'src/pages/donor/EditProfileForm';
import { AuthContext } from 'src/context/AuthContext';
import authConfig from 'src/configs/auth';

// Mock dependencies
jest.mock('axios');
jest.mock('src/helpers/utils', () => ({
  getUrl: jest.fn((endpoint) => `https://api.test.com${endpoint}`),
  getAuthorizationHeaders: jest.fn(() => ({
    'Authorization': 'Bearer test-token',
    'Content-Type': 'application/json',
  })),
}));

// Mock Material-UI components to avoid complex rendering issues
jest.mock('@mui/material/TextField', () => {
  return function MockTextField(props) {
    return (
      <input
        {...props}
        data-testid={props['data-testid'] || `textfield-${props.label?.toLowerCase().replace(/\s+/g, '-')}`}
        onChange={(e) => props.onChange && props.onChange(e)}
        value={props.value || ''}
      />
    );
  };
});

jest.mock('@mui/material/Button', () => {
  return React.forwardRef(function MockButton(props, ref) {
    return (
      <button
        {...props}
        ref={ref}
        data-testid={props['data-testid'] || `button-${props.children?.toLowerCase().replace(/\s+/g, '-')}`}
        onClick={props.onClick}
        type={props.type || 'button'}
      >
        {props.children}
      </button>
    );
  });
});

jest.mock('src/@core/components/custom-components/MobileNumberValidation', () => {
  return function MockMobileNumberValidation(props) {
    return (
      <input
        {...props}
        data-testid="mobile-number-input"
        type="text"
        onChange={(e) => props.onChange && props.onChange(e)}
        value={props.value || ''}
      />
    );
  };
});

jest.mock('src/@core/components/custom-components/SelectAutoComplete', () => {
  return function MockSelectAutoComplete({ nameArray, value, onChange, label, ...props }) {
    return (
      <select
        {...props}
        data-testid={`select-${label?.toLowerCase().replace(/\s+/g, '-')}`}
        value={value || ''}
        onChange={onChange}
      >
        <option value="">Select {label}</option>
        {nameArray?.map((item) => (
          <option key={item.value} value={item.value}>
            {item.key}
          </option>
        ))}
      </select>
    );
  };
});

describe('EditProfileForm', () => {
  const mockAuthContext = {
    user: {
      id: 1,
      orgId: 123,
      email: '<EMAIL>',
      name: 'Test Donor',
    },
    listValues: [
      { id: 1, name: 'Individual' },
      { id: 2, name: 'Entity' },
      { id: 3, name: 'Social Media' },
      { id: 4, name: 'Any Other' },
    ],
    getAllListValuesByListNameId: jest.fn(),
  };

  const mockProfileData = {
    id: 1,
    name: 'John Donor',
    email: '<EMAIL>',
    contactNumber: '**********',
    donorType: 1,
    panNo: '**********',
    donorReferralSource: 3,
    address: '123 Test Street',
    state: 'Test State',
    pinCode: '123456',
    donorOrgName: 'Test Org',
    donorReferralSourceAnyOther: 'Other source',
  };

  const mockProps = {
    profileData: mockProfileData,
    setIsEditing: jest.fn(),
    fetchOrganisationData: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock getAllListValuesByListNameId to call success callback
    mockAuthContext.getAllListValuesByListNameId.mockImplementation((listNameId, successCallback) => {
      const mockData = {
        listValues: [
          { id: 1, listValue: 'Individual' },
          { id: 2, listValue: 'Entity' },
          { id: 3, listValue: 'Social Media' },
          { id: 4, listValue: 'Any Other' },
        ]
      };
      successCallback(mockData);
    });

    axios.mockResolvedValue({ data: { success: true } });
  });

  const renderEditProfileForm = (contextOverrides = {}, propsOverrides = {}) => {
    const authContextValue = { ...mockAuthContext, ...contextOverrides };
    const props = { ...mockProps, ...propsOverrides };
    
    return render(
      <AuthContext.Provider value={authContextValue}>
        <EditProfileForm {...props} />
      </AuthContext.Provider>
    );
  };

  describe('Basic Rendering Tests', () => {
    test('should render form without crashing', () => {
      renderEditProfileForm();
      
      // Just check that the component renders without throwing
      expect(document.body).toBeInTheDocument();
    });

    test('should call getAllListValuesByListNameId on mount', () => {
      renderEditProfileForm();

      expect(mockAuthContext.getAllListValuesByListNameId).toHaveBeenCalledWith(
        authConfig.donorTypeListNameId,
        expect.any(Function),
        expect.any(Function)
      );

      expect(mockAuthContext.getAllListValuesByListNameId).toHaveBeenCalledWith(
        authConfig.referralSourceListNameId,
        expect.any(Function),
        expect.any(Function)
      );
    });

    test('should handle missing profile data', () => {
      renderEditProfileForm({}, { profileData: null });
      
      // Should render without crashing
      expect(document.body).toBeInTheDocument();
    });

    test('should handle missing user context', () => {
      renderEditProfileForm({ user: null });
      
      // Should render without crashing
      expect(document.body).toBeInTheDocument();
    });
  });

  describe('Form Interaction Tests', () => {
    test('should handle cancel button click', async () => {
      const user = userEvent.setup();
      renderEditProfileForm();

      // Find and click cancel button (may be rendered by Material-UI)
      const cancelButtons = screen.getAllByText(/cancel/i);
      if (cancelButtons.length > 0) {
        await user.click(cancelButtons[0]);
        expect(mockProps.setIsEditing).toHaveBeenCalledWith(false);
      }
    });

    test('should render save button and handle click', async () => {
      const user = userEvent.setup();
      renderEditProfileForm();

      // Wait for form to be fully rendered
      await waitFor(() => {
        expect(screen.getByText(/save/i)).toBeInTheDocument();
      });

      // Find save button
      const saveButton = screen.getByText(/save/i);
      expect(saveButton).toBeInTheDocument();
      expect(saveButton).toHaveAttribute('type', 'button');

      // Click save button (this will trigger form validation and submission)
      await user.click(saveButton);

      // The button should be clickable and not disabled
      expect(saveButton).not.toBeDisabled();
    });
  });

  describe('Data Loading Error Handling', () => {
    test('should handle getAllListValuesByListNameId error', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      mockAuthContext.getAllListValuesByListNameId.mockImplementation((listNameId, successCallback, errorCallback) => {
        errorCallback(new Error('Loading error'));
      });

      renderEditProfileForm();

      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    test('should handle API submission error', async () => {
      const user = userEvent.setup();
      axios.mockRejectedValueOnce(new Error('API Error'));
      
      renderEditProfileForm();

      // Try to submit form
      const saveButtons = screen.getAllByText(/save/i);
      if (saveButtons.length > 0) {
        await user.click(saveButtons[0]);
        
        // Should handle error gracefully
        await waitFor(() => {
          // Component should still be rendered
          expect(document.body).toBeInTheDocument();
        });
      }
    });
  });

  describe('Different Profile Data Scenarios', () => {
    test('should handle entity type profile data', () => {
      const entityProfileData = { ...mockProfileData, donorType: 2 };
      renderEditProfileForm({}, { profileData: entityProfileData });
      
      // Should render without crashing
      expect(document.body).toBeInTheDocument();
    });

    test('should handle "Any Other" referral source', () => {
      const otherReferralData = { ...mockProfileData, donorReferralSource: 4 };
      renderEditProfileForm({}, { profileData: otherReferralData });
      
      // Should render without crashing
      expect(document.body).toBeInTheDocument();
    });

    test('should handle profile data with null values', () => {
      const nullProfileData = {
        ...mockProfileData,
        address: null,
        state: null,
        pinCode: null,
        donorOrgName: null,
        donorReferralSourceAnyOther: null,
      };
      
      renderEditProfileForm({}, { profileData: nullProfileData });
      
      // Should render without crashing
      expect(document.body).toBeInTheDocument();
    });
  });

  describe('Component Lifecycle Tests', () => {
    test('should handle component unmount', () => {
      const { unmount } = renderEditProfileForm();
      
      // Should unmount without errors
      expect(() => unmount()).not.toThrow();
    });

    test('should handle props changes', () => {
      const { rerender } = renderEditProfileForm();
      
      const newProfileData = { ...mockProfileData, name: 'Updated Name' };
      
      rerender(
        <AuthContext.Provider value={mockAuthContext}>
          <EditProfileForm {...mockProps} profileData={newProfileData} />
        </AuthContext.Provider>
      );
      
      // Should handle prop changes without crashing
      expect(document.body).toBeInTheDocument();
    });
  });

  describe('Form Validation Tests', () => {
    test('should validate PAN number format', async () => {
      const user = userEvent.setup();
      renderEditProfileForm();

      const panInput = screen.getByPlaceholderText('Enter your PAN Number');

      // Test invalid PAN format
      await user.clear(panInput);
      await user.type(panInput, 'INVALID123');

      // Should show validation error
      await waitFor(() => {
        expect(panInput).toHaveValue('INVALID123');
      });
    });

    test('should handle donor type selection', async () => {
      const user = userEvent.setup();
      renderEditProfileForm();

      const donorTypeSelect = screen.getByTestId('select-donor-type');

      // Change donor type
      await user.selectOptions(donorTypeSelect, '2');

      await waitFor(() => {
        expect(donorTypeSelect).toHaveValue('2');
      });
    });

    test('should handle referral source selection', async () => {
      const user = userEvent.setup();
      renderEditProfileForm();

      const referralSourceSelect = screen.getByTestId('select-referral-source');

      // Change referral source
      await user.selectOptions(referralSourceSelect, '3');

      await waitFor(() => {
        expect(referralSourceSelect).toHaveValue('3');
      });
    });

    test('should show organization name field for entity type', async () => {
      const entityProfileData = {
        ...mockProfileData,
        donorType: 2 // Entity type
      };

      render(
        <AuthContext.Provider value={mockAuthContext}>
          <EditProfileForm
            {...mockProps}
            profileData={entityProfileData}
          />
        </AuthContext.Provider>
      );

      // Should show organization name field for entity type
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Enter your organization name')).toBeInTheDocument();
      });
    });

    test('should show "Any Other" field when referral source is "Any Other"', async () => {
      const anyOtherProfileData = {
        ...mockProfileData,
        donorReferralSource: 4, // Any Other
        donorReferralSourceAnyOther: 'Custom referral source'
      };

      render(
        <AuthContext.Provider value={mockAuthContext}>
          <EditProfileForm
            {...mockProps}
            profileData={anyOtherProfileData}
          />
        </AuthContext.Provider>
      );

      // Should show "Any Other" text field
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Enter Any Other Referral Source')).toBeInTheDocument();
        expect(screen.getByDisplayValue('Custom referral source')).toBeInTheDocument();
      });
    });
  });

  describe('Field Interaction Tests', () => {
    test('should handle mobile number input with validation', async () => {
      const user = userEvent.setup();
      renderEditProfileForm();

      const mobileInput = screen.getByTestId('mobile-number-input');

      // Clear and type new mobile number
      await user.clear(mobileInput);
      await user.type(mobileInput, '**********');

      await waitFor(() => {
        expect(mobileInput).toHaveValue('**********');
      });
    });

    test('should handle PAN number uppercase conversion', async () => {
      const user = userEvent.setup();
      renderEditProfileForm();

      const panInput = screen.getByPlaceholderText('Enter your PAN Number');

      // Type lowercase PAN number
      await user.clear(panInput);
      await user.type(panInput, 'abcde1234f');

      // Should convert to uppercase
      await waitFor(() => {
        expect(panInput).toHaveValue('**********');
      });
    });

    test('should handle address field input', async () => {
      const user = userEvent.setup();
      renderEditProfileForm();

      const addressInput = screen.getByTestId('textfield-address');

      // Type address
      await user.clear(addressInput);
      await user.type(addressInput, '123 Test Street, Test City');

      await waitFor(() => {
        expect(addressInput).toHaveValue('123 Test Street, Test City');
      });
    });

    test('should handle state field input', async () => {
      const user = userEvent.setup();
      renderEditProfileForm();

      const stateInput = screen.getByTestId('textfield-state');

      // Type state
      await user.clear(stateInput);
      await user.type(stateInput, 'Test State');

      await waitFor(() => {
        expect(stateInput).toHaveValue('Test State');
      });
    });

    test('should handle PIN code input', async () => {
      const user = userEvent.setup();
      renderEditProfileForm();

      const pinInput = screen.getByTestId('textfield-pin-code');

      // Type PIN code
      await user.clear(pinInput);
      await user.type(pinInput, '123456');

      await waitFor(() => {
        expect(pinInput).toHaveValue('123456');
      });
    });
  });

  describe('Error Handling Tests', () => {
    test('should handle API error during form submission', async () => {
      const user = userEvent.setup();
      renderEditProfileForm();

      const saveButton = screen.getByText(/save/i);
      await user.click(saveButton);

      // Should handle error gracefully - button should remain clickable
      expect(saveButton).not.toBeDisabled();
      expect(saveButton).toBeInTheDocument();
    });

    test('should handle missing profile data gracefully', () => {
      render(
        <AuthContext.Provider value={mockAuthContext}>
          <EditProfileForm
            {...mockProps}
            profileData={null}
          />
        </AuthContext.Provider>
      );

      // Should render without crashing
      expect(screen.getByText(/save/i)).toBeInTheDocument();
    });

    test('should handle empty profile data', () => {
      render(
        <AuthContext.Provider value={mockAuthContext}>
          <EditProfileForm
            {...mockProps}
            profileData={{}}
          />
        </AuthContext.Provider>
      );

      // Should render without crashing
      expect(screen.getByText(/save/i)).toBeInTheDocument();
    });
  });

  describe('Form Submission Tests', () => {
    test('should handle cancel button click', async () => {
      const user = userEvent.setup();
      renderEditProfileForm();

      const cancelButton = screen.getByText(/cancel/i);
      await user.click(cancelButton);

      // Should call setIsEditing with false
      expect(mockProps.setIsEditing).toHaveBeenCalledWith(false);
    });

    test('should handle form submission with valid data', async () => {
      const user = userEvent.setup();
      renderEditProfileForm();

      // Fill in some form data
      const nameInput = screen.getByTestId('textfield-name');
      await user.clear(nameInput);
      await user.type(nameInput, 'Updated Name');

      const saveButton = screen.getByText(/save/i);

      // Test that save button is clickable and form has updated data
      expect(saveButton).toBeInTheDocument();
      expect(nameInput).toHaveValue('Updated Name');

      await user.click(saveButton);

      // Button should remain enabled after click
      expect(saveButton).not.toBeDisabled();
    });

    test('should handle form submission with entity type', async () => {
      const entityProfileData = {
        ...mockProfileData,
        donorType: 2 // Entity type
      };

      const user = userEvent.setup();
      render(
        <AuthContext.Provider value={mockAuthContext}>
          <EditProfileForm
            {...mockProps}
            profileData={entityProfileData}
          />
        </AuthContext.Provider>
      );

      // Should show organization name field for entity type
      await waitFor(() => {
        expect(screen.getByTestId('textfield-organization-name*')).toBeInTheDocument();
      });

      const saveButton = screen.getByText(/save/i);
      await user.click(saveButton);

      // Button should be clickable
      expect(saveButton).not.toBeDisabled();
    });
  });

  describe('Edge Cases', () => {
    test('should handle very long input values', async () => {
      const user = userEvent.setup();
      renderEditProfileForm();

      const nameInput = screen.getByTestId('textfield-name');
      const longName = 'A'.repeat(100);

      await user.clear(nameInput);
      await user.type(nameInput, longName);

      await waitFor(() => {
        expect(nameInput).toHaveValue(longName);
      });
    });

    test('should handle special characters in input', async () => {
      const user = userEvent.setup();
      renderEditProfileForm();

      const nameInput = screen.getByTestId('textfield-name');
      const specialName = 'John@#$%^&*()_+Doe';

      await user.clear(nameInput);
      await user.type(nameInput, specialName);

      await waitFor(() => {
        expect(nameInput).toHaveValue(specialName);
      });
    });

    test('should handle rapid form interactions', async () => {
      const user = userEvent.setup();
      renderEditProfileForm();

      const nameInput = screen.getByTestId('textfield-name');
      const panInput = screen.getByTestId('textfield-pan-number');

      // Rapid typing in multiple fields
      await user.type(nameInput, 'Fast');
      await user.type(panInput, 'QUICK');

      await waitFor(() => {
        expect(nameInput).toHaveValue('John DonorFast');
        expect(panInput).toHaveValue('**********QUICK');
      });
    });
  });
});
