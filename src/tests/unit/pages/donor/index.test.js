import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import axios from 'axios';
import DonorProfile from 'src/pages/donor/index';
import { AuthProvider, AuthContext } from 'src/context/AuthContext';
import authConfig from 'src/configs/auth';

// Mock dependencies
jest.mock('axios');
jest.mock('src/helpers/utils', () => ({
  getUrl: jest.fn((endpoint) => `https://api.test.com${endpoint}`),
  getAuthorizationHeaders: jest.fn(() => ({
    'Authorization': 'Bearer test-token',
    'Content-Type': 'application/json',
  })),
}));

jest.mock('src/pages/donor/EditProfileForm', () => {
  return function MockEditProfileForm({ profileData, setIsEditing, fetchOrganisationData }) {
    return (
      <div data-testid="edit-profile-form">
        <h3>Edit Profile Form</h3>
        <input
          data-testid="edit-name"
          defaultValue={profileData?.name || ''}
          placeholder="Enter name"
        />
        <input
          data-testid="edit-email"
          defaultValue={profileData?.email || ''}
          placeholder="Enter email"
        />
        <input
          data-testid="edit-mobile"
          defaultValue={profileData?.contactNumber || ''}
          placeholder="Enter mobile"
        />
        <input
          data-testid="edit-pan"
          defaultValue={profileData?.panNo || ''}
          placeholder="Enter PAN"
        />
        {profileData?.donorType === 2 && (
          <input
            data-testid="edit-org-name"
            defaultValue={profileData?.donorOrgName || ''}
            placeholder="Enter organization name"
          />
        )}
        {profileData?.donorReferralSource === 4 && (
          <input
            data-testid="edit-other-referral"
            defaultValue={profileData?.donorReferralSourceAnyOther || ''}
            placeholder="Enter other referral source"
          />
        )}
        <button
          data-testid="save-button"
          onClick={() => {
            fetchOrganisationData();
            setIsEditing(false);
          }}
        >
          Save
        </button>
        <button
          data-testid="cancel-button"
          onClick={() => setIsEditing(false)}
        >
          Cancel
        </button>
      </div>
    );
  };
});

describe('DonorProfile', () => {
  const mockAuthContext = {
    user: {
      id: 1,
      orgId: 123,
      email: '<EMAIL>',
      name: 'Test Donor',
    },
    listValues: [
      { id: 1, name: 'Individual' },
      { id: 2, name: 'Entity' },
      { id: 3, name: 'Social Media' },
      { id: 4, name: 'Any Other' },
    ],
  };

  const mockProfileData = {
    id: 1,
    name: 'John Donor',
    email: '<EMAIL>',
    contactNumber: '**********',
    donorType: 1,
    panNo: '**********',
    donorReferralSource: 3,
    address: '123 Test Street',
    state: 'Test State',
    donorOrgName: 'Test Org',
    donorReferralSourceAnyOther: 'Other source',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock axios
    axios.mockImplementation(() => Promise.resolve({ data: mockProfileData }));
  });

  const renderDonorProfile = (contextOverrides = {}) => {
    const authContextValue = { ...mockAuthContext, ...contextOverrides };

    return render(
      <AuthContext.Provider value={authContextValue}>
        <DonorProfile />
      </AuthContext.Provider>
    );
  };

  describe('Profile Display Tests', () => {
    test('should render donor profile page', async () => {
      renderDonorProfile();

      expect(screen.getByText('Donor Information')).toBeInTheDocument();
      
      await waitFor(() => {
        expect(axios).toHaveBeenCalledWith({
          method: 'get',
          url: 'https://api.test.com' + authConfig.organisationsEndpoint + '/donor/' + mockAuthContext.user.orgId,
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-token',
          }),
        });
      });
    });

    test('should display profile fields when data is loaded', async () => {
      renderDonorProfile();

      await waitFor(() => {
        expect(screen.getByText('John Donor')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByText('**********')).toBeInTheDocument();
        expect(screen.getByText('**********')).toBeInTheDocument();
      });

      // Check field labels
      expect(screen.getByText('Name')).toBeInTheDocument();
      expect(screen.getByText('Email')).toBeInTheDocument();
      expect(screen.getByText('Mobile Number')).toBeInTheDocument();
      expect(screen.getByText('PAN Number')).toBeInTheDocument();
    });

    test('should display donor type with proper mapping', async () => {
      renderDonorProfile();

      await waitFor(() => {
        expect(screen.getByText('Individual')).toBeInTheDocument();
      });
    });

    test('should display referral source with proper mapping', async () => {
      renderDonorProfile();

      await waitFor(() => {
        expect(screen.getByText('Social Media')).toBeInTheDocument();
      });
    });

    test('should show organization name for entity type donors', async () => {
      const entityProfileData = {
        ...mockProfileData,
        donorType: 2, // Entity type
      };
      
      axios.mockResolvedValueOnce({ data: entityProfileData });
      renderDonorProfile();

      await waitFor(() => {
        expect(screen.getByText('Organisation Name')).toBeInTheDocument();
        expect(screen.getByText('Test Org')).toBeInTheDocument();
      });
    });

    test('should show other referral source for "Any Other" selection', async () => {
      const otherReferralData = {
        ...mockProfileData,
        donorReferralSource: 4, // Any Other
      };
      
      axios.mockResolvedValueOnce({ data: otherReferralData });
      renderDonorProfile();

      await waitFor(() => {
        expect(screen.getByText('Other Referral Source')).toBeInTheDocument();
        expect(screen.getByText('Other source')).toBeInTheDocument();
      });
    });

    test('should show dash for empty fields', async () => {
      const emptyProfileData = {
        ...mockProfileData,
        address: null,
        state: '',
      };
      
      axios.mockResolvedValueOnce({ data: emptyProfileData });
      renderDonorProfile();

      await waitFor(() => {
        // Should show dashes for empty values
        const dashElements = screen.getAllByText('-');
        expect(dashElements.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Edit Functionality Tests', () => {
    test('should show edit icon on hover', async () => {
      renderDonorProfile();

      await waitFor(() => {
        expect(screen.getByText('John Donor')).toBeInTheDocument();
      });

      const editButton = screen.getByLabelText('Edit Profile');
      expect(editButton).toBeInTheDocument();
    });

    test('should open edit form when edit button is clicked', async () => {
      const user = userEvent.setup();
      renderDonorProfile();

      await waitFor(() => {
        expect(screen.getByText('John Donor')).toBeInTheDocument();
      });

      const editButton = screen.getByLabelText('Edit Profile');
      await user.click(editButton);

      expect(screen.getByTestId('edit-profile-form')).toBeInTheDocument();
      expect(screen.getByText('Edit Profile Form')).toBeInTheDocument();
    });

    test('should hide edit icon when in edit mode', async () => {
      const user = userEvent.setup();
      renderDonorProfile();

      await waitFor(() => {
        expect(screen.getByText('John Donor')).toBeInTheDocument();
      });

      const editButton = screen.getByLabelText('Edit Profile');
      await user.click(editButton);

      expect(screen.queryByLabelText('Edit Profile')).not.toBeInTheDocument();
    });

    test('should pass profile data to edit form', async () => {
      const user = userEvent.setup();
      renderDonorProfile();

      await waitFor(() => {
        expect(screen.getByText('John Donor')).toBeInTheDocument();
      });

      const editButton = screen.getByLabelText('Edit Profile');
      await user.click(editButton);

      // Check that form is populated with existing data
      expect(screen.getByDisplayValue('John Donor')).toBeInTheDocument();
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByDisplayValue('**********')).toBeInTheDocument();
      expect(screen.getByDisplayValue('**********')).toBeInTheDocument();
    });
  });

  describe('Edit Form Interaction Tests', () => {
    test('should save changes and exit edit mode', async () => {
      const user = userEvent.setup();
      renderDonorProfile();

      await waitFor(() => {
        expect(screen.getByText('John Donor')).toBeInTheDocument();
      });

      // Enter edit mode
      const editButton = screen.getByLabelText('Edit Profile');
      await user.click(editButton);

      // Make changes
      const nameInput = screen.getByTestId('edit-name');
      await user.clear(nameInput);
      await user.type(nameInput, 'Updated Donor Name');

      // Save changes
      const saveButton = screen.getByTestId('save-button');
      await user.click(saveButton);

      // Should exit edit mode and refetch data
      expect(screen.queryByTestId('edit-profile-form')).not.toBeInTheDocument();
      await waitFor(() => {
        expect(axios).toHaveBeenCalledTimes(2); // Initial load + refetch after save
      });
    });

    test('should cancel editing and return to view mode', async () => {
      const user = userEvent.setup();
      renderDonorProfile();

      await waitFor(() => {
        expect(screen.getByText('John Donor')).toBeInTheDocument();
      });

      // Enter edit mode
      const editButton = screen.getByLabelText('Edit Profile');
      await user.click(editButton);

      // Cancel editing
      const cancelButton = screen.getByTestId('cancel-button');
      await user.click(cancelButton);

      // Should exit edit mode without saving
      expect(screen.queryByTestId('edit-profile-form')).not.toBeInTheDocument();
      expect(screen.getByText('John Donor')).toBeInTheDocument();
    });
  });

  describe('Data Loading Tests', () => {
    test('should fetch donor profile data on mount', async () => {
      renderDonorProfile();

      await waitFor(() => {
        expect(axios).toHaveBeenCalledWith({
          method: 'get',
          url: 'https://api.test.com' + authConfig.organisationsEndpoint + '/donor/' + mockAuthContext.user.orgId,
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-token',
          }),
        });
      });
    });

    test('should handle API errors gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      axios.mockRejectedValueOnce(new Error('API Error'));

      renderDonorProfile();

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('CHS Data error', expect.any(Error));
      });

      consoleSpy.mockRestore();
    });

    test('should show empty state when no profile data', async () => {
      axios.mockResolvedValueOnce({ data: null });
      renderDonorProfile();

      expect(screen.getByText('Donor Information')).toBeInTheDocument();
      
      await waitFor(() => {
        // Should still render the component structure
        expect(screen.getByText('Name')).toBeInTheDocument();
      });
    });

    test('should refetch data after successful edit', async () => {
      const user = userEvent.setup();
      renderDonorProfile();

      await waitFor(() => {
        expect(screen.getByText('John Donor')).toBeInTheDocument();
      });

      // Enter edit mode and save
      const editButton = screen.getByLabelText('Edit Profile');
      await user.click(editButton);

      const saveButton = screen.getByTestId('save-button');
      await user.click(saveButton);

      // Should refetch data
      await waitFor(() => {
        expect(axios).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('List Values Integration Tests', () => {
    test('should handle missing list values gracefully', async () => {
      renderDonorProfile({
        listValues: null,
      });

      await waitFor(() => {
        expect(screen.getByText('Donor Information')).toBeInTheDocument();
      });

      // Should show dashes for unmappable values
      await waitFor(() => {
        const dashElements = screen.getAllByText('-');
        expect(dashElements.length).toBeGreaterThan(0);
      });
    });

    test('should map donor type correctly with list values', async () => {
      const customListValues = [
        { id: 1, name: 'Custom Individual' },
        { id: 2, name: 'Custom Entity' },
      ];

      renderDonorProfile({
        listValues: customListValues,
      });

      await waitFor(() => {
        expect(screen.getByText('Custom Individual')).toBeInTheDocument();
      });
    });

    test('should handle unmatched list value IDs', async () => {
      const profileWithUnmatchedType = {
        ...mockProfileData,
        donorType: 999, // Non-existent ID
      };

      axios.mockResolvedValueOnce({ data: profileWithUnmatchedType });
      renderDonorProfile();

      await waitFor(() => {
        // Should show dash for unmatched donor type
        expect(screen.getByText('Donor Type')).toBeInTheDocument();
      });
    });
  });

  describe('User Context Tests', () => {
    test('should use correct user orgId for API call', async () => {
      const customUser = {
        ...mockAuthContext.user,
        orgId: 456,
      };

      renderDonorProfile({
        user: customUser,
      });

      await waitFor(() => {
        expect(axios).toHaveBeenCalledWith({
          method: 'get',
          url: 'https://api.test.com' + authConfig.organisationsEndpoint + '/donor/456',
          headers: expect.any(Object),
        });
      });
    });

    test('should handle missing user gracefully', async () => {
      renderDonorProfile({
        user: null,
      });

      // Should not crash with null user
      expect(screen.getByText('Donor Information')).toBeInTheDocument();
    });
  });

  describe('Responsive Design Tests', () => {
    test('should render properly in container layout', () => {
      renderDonorProfile();

      // Check for container and card structure
      expect(screen.getByText('Donor Information')).toBeInTheDocument();
      
      // The component should be wrapped in proper Material-UI containers
      const donorInfoElement = screen.getByText('Donor Information');
      expect(donorInfoElement.closest('.MuiContainer-root')).toBeInTheDocument();
    });

    test('should handle long text content properly', async () => {
      const longContentData = {
        ...mockProfileData,
        name: 'Very Long Donor Name That Might Wrap Multiple Lines',
        address: 'Very Long Address That Contains Multiple Lines And Might Cause Layout Issues',
      };

      axios.mockResolvedValueOnce({ data: longContentData });
      renderDonorProfile();

      await waitFor(() => {
        expect(screen.getByText('Very Long Donor Name That Might Wrap Multiple Lines')).toBeInTheDocument();
        expect(screen.getByText('Very Long Address That Contains Multiple Lines And Might Cause Layout Issues')).toBeInTheDocument();
      });
    });
  });

  describe('Field Visibility Logic Tests', () => {
    test('should show organization name only for entity type', async () => {
      // Test Individual type (should not show org name)
      const individualData = { ...mockProfileData, donorType: 1 };
      axios.mockResolvedValueOnce({ data: individualData });
      
      renderDonorProfile();

      await waitFor(() => {
        expect(screen.queryByText('Organisation Name')).not.toBeInTheDocument();
      });
    });

    test('should show other referral source only for "Any Other" selection', async () => {
      // Test with non-"Any Other" referral source
      const regularReferralData = { ...mockProfileData, donorReferralSource: 3 };
      axios.mockResolvedValueOnce({ data: regularReferralData });
      
      renderDonorProfile();

      await waitFor(() => {
        expect(screen.queryByText('Other Referral Source')).not.toBeInTheDocument();
      });
    });
  });

  describe('Error Boundary Tests', () => {
    test('should handle render errors gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // Mock a component error
      renderDonorProfile({
        listValues: undefined, // This might cause errors in some scenarios
      });

      // Should not crash the application
      expect(screen.getByText('Donor Information')).toBeInTheDocument();

      consoleSpy.mockRestore();
    });
  });

  describe('EditProfileForm Integration Tests', () => {
    test('should render EditProfileForm with correct props when editing', async () => {
      const user = userEvent.setup();
      renderDonorProfile();

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('John Donor')).toBeInTheDocument();
      });

      // Click edit button
      const editButton = screen.getByLabelText('Edit Profile');
      await user.click(editButton);

      // Check that EditProfileForm is rendered with correct props
      await waitFor(() => {
        expect(screen.getByTestId('save-button')).toBeInTheDocument();
        expect(screen.getByTestId('cancel-button')).toBeInTheDocument();
      });
    });

    test('should handle form field changes in EditProfileForm', async () => {
      const user = userEvent.setup();
      renderDonorProfile();

      // Wait for data to load and enter edit mode
      await waitFor(() => {
        expect(screen.getByText('John Donor')).toBeInTheDocument();
      });

      const editButton = screen.getByLabelText('Edit Profile');
      await user.click(editButton);

      // Wait for form to render and test field interactions
      await waitFor(() => {
        expect(screen.getByTestId('edit-name')).toBeInTheDocument();
      });

      // Test changing name field
      const nameInput = screen.getByTestId('edit-name');
      await user.clear(nameInput);
      await user.type(nameInput, 'Updated Name');
      expect(screen.getByDisplayValue('Updated Name')).toBeInTheDocument();

      // Test changing mobile field
      const mobileInput = screen.getByTestId('edit-mobile');
      await user.clear(mobileInput);
      await user.type(mobileInput, '9999999999');
      expect(screen.getByDisplayValue('9999999999')).toBeInTheDocument();

      // Test changing PAN field
      const panInput = screen.getByTestId('edit-pan');
      await user.clear(panInput);
      await user.type(panInput, 'NEWPAN123Z');
      expect(screen.getByDisplayValue('NEWPAN123Z')).toBeInTheDocument();
    });

    test('should handle EditProfileForm with entity type donor data', async () => {
      const user = userEvent.setup();
      const entityProfileData = {
        ...mockProfileData,
        donorType: 2, // Entity type
        donorOrgName: 'Test Organization'
      };

      axios.mockResolvedValueOnce({ data: entityProfileData });
      renderDonorProfile();

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('Test Organization')).toBeInTheDocument();
      });

      // Click edit button
      const editButton = screen.getByLabelText('Edit Profile');
      await user.click(editButton);

      // Check that form is populated with entity data
      await waitFor(() => {
        expect(screen.getByDisplayValue('John Donor')).toBeInTheDocument();
        expect(screen.getByTestId('edit-org-name')).toBeInTheDocument();
        expect(screen.getByDisplayValue('Test Organization')).toBeInTheDocument();
      });
    });

    test('should handle EditProfileForm with "Any Other" referral source', async () => {
      const user = userEvent.setup();
      const otherReferralData = {
        ...mockProfileData,
        donorReferralSource: 4, // Any Other
        donorReferralSourceAnyOther: 'Custom referral source'
      };

      axios.mockResolvedValueOnce({ data: otherReferralData });
      renderDonorProfile();

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('Custom referral source')).toBeInTheDocument();
      });

      // Click edit button
      const editButton = screen.getByLabelText('Edit Profile');
      await user.click(editButton);

      // Check that form shows the custom referral source
      await waitFor(() => {
        expect(screen.getByDisplayValue('John Donor')).toBeInTheDocument();
      });
    });

    test('should handle form submission with validation', async () => {
      const user = userEvent.setup();
      renderDonorProfile();

      // Wait for data to load and enter edit mode
      await waitFor(() => {
        expect(screen.getByText('John Donor')).toBeInTheDocument();
      });

      const editButton = screen.getByLabelText('Edit Profile');
      await user.click(editButton);

      // Clear required field to test validation
      await waitFor(() => {
        expect(screen.getByTestId('edit-name')).toBeInTheDocument();
      });

      const nameInput = screen.getByTestId('edit-name');
      await user.clear(nameInput);

      // Try to submit with empty name
      const saveButton = screen.getByTestId('save-button');
      await user.click(saveButton);

      // Should still exit edit mode and refetch data (mock doesn't validate)
      await waitFor(() => {
        expect(axios).toHaveBeenCalledTimes(2); // Initial load + after save
      });
    });

    test('should handle multiple edit sessions', async () => {
      const user = userEvent.setup();
      renderDonorProfile();

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('John Donor')).toBeInTheDocument();
      });

      // First edit session
      const editButton = screen.getByLabelText('Edit Profile');
      await user.click(editButton);

      await waitFor(() => {
        expect(screen.getByTestId('cancel-button')).toBeInTheDocument();
      });

      const cancelButton = screen.getByTestId('cancel-button');
      await user.click(cancelButton);

      // Second edit session
      await waitFor(() => {
        expect(screen.getByLabelText('Edit Profile')).toBeInTheDocument();
      });

      const editButton2 = screen.getByLabelText('Edit Profile');
      await user.click(editButton2);

      await waitFor(() => {
        expect(screen.getByTestId('save-button')).toBeInTheDocument();
      });

      const saveButton = screen.getByTestId('save-button');
      await user.click(saveButton);

      // Should refetch data after save
      await waitFor(() => {
        expect(axios).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('Additional Coverage Tests', () => {
    test('should handle profile data with all fields populated', async () => {
      const completeProfileData = {
        ...mockProfileData,
        address: '123 Complete Street',
        state: 'Complete State',
        pinCode: '123456',
        donorType: 2,
        donorOrgName: 'Complete Organization',
        donorReferralSource: 4,
        donorReferralSourceAnyOther: 'Complete Other Source'
      };

      axios.mockResolvedValueOnce({ data: completeProfileData });
      renderDonorProfile();

      await waitFor(() => {
        expect(screen.getByText('John Donor')).toBeInTheDocument();
        expect(screen.getByText('123 Complete Street')).toBeInTheDocument();
        expect(screen.getByText('Complete State')).toBeInTheDocument();
        expect(screen.getByText('123456')).toBeInTheDocument();
        expect(screen.getByText('Complete Organization')).toBeInTheDocument();
        expect(screen.getByText('Complete Other Source')).toBeInTheDocument();
      });
    });

    test('should handle profile data with minimal fields', async () => {
      const minimalProfileData = {
        id: 1,
        name: 'Minimal User',
        email: '<EMAIL>',
        contactNumber: '1234567890',
        donorType: 1,
        donorReferralSource: 1,
      };

      axios.mockResolvedValueOnce({ data: minimalProfileData });
      renderDonorProfile();

      await waitFor(() => {
        expect(screen.getByText('Minimal User')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByText('1234567890')).toBeInTheDocument();
      });

      // Should show dashes for missing fields
      const dashElements = screen.getAllByText('-');
      expect(dashElements.length).toBeGreaterThan(0);
    });

    test('should handle different donor types correctly', async () => {
      // Test with different donor type values
      for (const donorType of [1, 2, 3]) {
        const profileData = { ...mockProfileData, donorType };
        axios.mockResolvedValueOnce({ data: profileData });

        const { unmount } = renderDonorProfile();

        await waitFor(() => {
          expect(screen.getByText('John Donor')).toBeInTheDocument();
        });

        unmount();
        jest.clearAllMocks();
      }
    });

    test('should handle different referral sources correctly', async () => {
      // Test with different referral source values
      for (const referralSource of [1, 2, 3, 4]) {
        const profileData = { ...mockProfileData, donorReferralSource: referralSource };
        axios.mockResolvedValueOnce({ data: profileData });

        const { unmount } = renderDonorProfile();

        await waitFor(() => {
          expect(screen.getByText('John Donor')).toBeInTheDocument();
        });

        unmount();
        jest.clearAllMocks();
      }
    });

    test('should handle API response with different data structures', async () => {
      // Test with nested data structure
      const nestedResponseData = {
        data: {
          donor: mockProfileData
        }
      };

      axios.mockResolvedValueOnce(nestedResponseData);
      renderDonorProfile();

      // Should handle the response gracefully even if structure is different
      expect(screen.getByText('Donor Information')).toBeInTheDocument();
    });

    test('should handle concurrent API calls', async () => {
      const user = userEvent.setup();
      renderDonorProfile();

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText('John Donor')).toBeInTheDocument();
      });

      // Quickly enter and exit edit mode multiple times
      const editButton = screen.getByLabelText('Edit Profile');
      await user.click(editButton);

      await waitFor(() => {
        expect(screen.getByTestId('save-button')).toBeInTheDocument();
      });

      const saveButton = screen.getByTestId('save-button');
      await user.click(saveButton);

      // Should handle concurrent calls gracefully
      await waitFor(() => {
        expect(axios).toHaveBeenCalledTimes(2);
      });
    });
  });
});