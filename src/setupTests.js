// Jest setup file
import '@testing-library/jest-dom';

// Suppress React act warnings in tests - these are expected in test environment
const originalConsoleError = console.error;
console.error = (message, ...args) => {
  if (
    typeof message === 'string' &&
    (message.includes('Warning: An update to') ||
     message.includes('was not wrapped in act') ||
     message.includes('You have enabled experimental feature') ||
     message.includes('Failed to fetch dashboard data') ||
     message.includes('Failed to fetch tenant data') ||
     message.includes('Failed to fetch donation heads') ||
     message.includes('Export failed'))
  ) {
    return;
  }
  originalConsoleError(message, ...args);
};

// Suppress Next.js experimental warnings
const originalConsoleWarn = console.warn;
console.warn = (message, ...args) => {
  if (
    typeof message === 'string' &&
    (message.includes('experimental feature') ||
     message.includes('esmExternals'))
  ) {
    return;
  }
  originalConsoleWarn(message, ...args);
};

// Mock Next.js router
const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  back: jest.fn(),
  pathname: '/test',
  query: {},
  asPath: '/test',
  isReady: true,
  route: '/test',
};

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => mockRouter),
}));

// Mock localStorage
global.localStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

// Mock sessionStorage
global.sessionStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};
