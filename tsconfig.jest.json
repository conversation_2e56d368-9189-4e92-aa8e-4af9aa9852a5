{"extends": "./tsconfig.json", "compilerOptions": {"jsx": "react-jsx", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": false, "noEmit": true, "skipLibCheck": true, "types": ["jest", "@testing-library/jest-dom", "node"]}, "include": ["src/**/*", "src/tests/**/*", "__mocks__/**/*"], "exclude": ["node_modules", ".next", "coverage"]}