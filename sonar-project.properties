sonar.projectKey=donation-receipt-frontend
sonar.projectName=Donation Receipt Frontend
sonar.host.url=http://localhost:9000
# sonar.token should be set via environment variable SONAR_TOKEN
# or passed as a command line parameter with -Dsonar.login=
# sonar.token=squ_35147b47676bfd3941b139fbe70cdc07894e535c
sonar.token=sqp_fca557f2a4925fe95b38d3473526409a2404e26b
sonar.sources=src
sonar.tests=src/tests
sonar.test.inclusions=src/tests/**
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.sourceEncoding=UTF-8
sonar.exclusions=src/@core/**,public/**,coverage/**,src/pages/search.js,src/pages/401.js,src/pages/donors/AdvancedSearch.js,src/pages/donation-receipts/Columns.js,src/pages/reports/**,src/pages/donation-receipts/ViewDialog.js,src/pages/donation-receipts/AdvancedSearch.js,src/pages/donation-head/AdvancedSearch.js,src/pages/404.js,src/pages/permission/RBACContext.js,src/pages/register/TermsAndConditionsDialog.js,src/pages/register/activate.js,src/pages/register/PrivacyPolicyDialog.js,src/pages/500.js,src/pages/_document.js,src/views/**,src/layouts/**,src/top-menu/**,src/pages/login-old/**,src/pages/acl/**,src/pages/SP/**,src/pages/dashboard/**,src/pages/styles/**,src/pages/houzer/**,src/pages/notifications/**,src/pages/approve-donors/**,src/hooks/**,src/constants/**,src/navigation/**,src/store/**,src/utils/**,src/pages/_app.js,src/helpers/**,src/context/**,src/@fake-db/**,src/@store/**,src/@quick-link/**,src/services/configService.ts,src/config/env.ts,src/main.jsx,src/setupTests.js,src/configs/**,src/iconify-bundle/**
sonar.coverage.exclusions=src/@core/**,public/**,src/utils/**,src/context/**,src/pages/_app.js,src/pages/reports/**,src/pages/donors/AdvancedSearch.js,src/pages/donation-receipts/Columns.js,src/pages/donation-receipts/ViewDialog.js,src/pages/donation-receipts/AdvancedSearch.js,src/pages/donation-head/AdvancedSearch.js,src/pages/register/TermsAndConditionsDialog.js,src/pages/register/activate.js,src/pages/register/PrivacyPolicyDialog.js,src/top-menu/**,src/pages/permission/RBACContext.js,src/pages/login-old/**,src/views/**,src/pages/search.js,src/pages/401.js,src/pages/404.js,src/pages/500.js,src/pages/_document.js,src/hooks/**,src/constants/**,src/pages/dashboard/**,src/pages/SP/**,src/pages/houzer/**,src/pages/styles/**,src/pages/acl/**,src/pages/approve-donors/**,src/pages/notifications/**,src/navigation/**,src/store/**,src/helpers/**,src/layouts/**,src/@fake-db/**,src/@store/**,src/@quick-link/**,src/services/configService.ts,src/config/env.ts,src/main.jsx,src/setupTests.js,src/configs/**,src/iconify-bundle/**

